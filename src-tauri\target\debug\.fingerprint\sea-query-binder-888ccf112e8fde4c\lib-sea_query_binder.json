{"rustc": 16591470773350601817, "features": "[\"bigdecimal\", \"chrono\", \"postgres-array\", \"rust_decimal\", \"serde_json\", \"sqlx\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"uuid\", \"with-bigdecimal\", \"with-chrono\", \"with-json\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "declared_features": "[\"bigdecimal\", \"chrono\", \"ipnetwork\", \"mac_address\", \"pgvector\", \"postgres-array\", \"postgres-vector\", \"runtime-actix\", \"runtime-actix-native-tls\", \"runtime-actix-rustls\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"serde_json\", \"sqlx\", \"sqlx-any\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"uuid\", \"with-bigdecimal\", \"with-chrono\", \"with-ipnetwork\", \"with-json\", \"with-mac_address\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "target": 2357794343378131723, "profile": 2241668132362809309, "path": 10650141606276709978, "deps": [[7161281228672193341, "sea_query", false, 17659451556468023092], [8319709847752024821, "uuid", false, 4375624582824923410], [9897246384292347999, "chrono", false, 11565035621207823088], [12409575957772518135, "time", false, 6202611441483771785], [14647456484942590313, "bigdecimal", false, 16021671231134990048], [15367738274754116744, "serde_json", false, 13370400617386028636], [16682465660942253309, "rust_decimal", false, 7608462804829237094], [17982831385697850842, "sqlx", false, 11940092348544038512]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sea-query-binder-888ccf112e8fde4c\\dep-lib-sea_query_binder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}