//! # Meditrack Application Library
//!
//! This is the main library crate for the Meditrack pharmacy management system.
//! It provides a modular, secure, and healthcare-compliant architecture with:
//!
//! ## Core Modules
//!
//! - `app`: Application lifecycle management and initialization
//! - `commands`: <PERSON><PERSON> commands with security validation
//! - `errors`: Comprehensive error handling and types
//! - `health_checks`: System health validation
//! - `logging`: HIPAA-compliant logging configuration
//! - `security`: Input validation and security measures
//! - `state`: Application state management
//! - `ipc`: Inter-process communication handlers
//!
//! ## Security Features
//!
//! - Input validation on all user commands
//! - Audit logging for HIPAA compliance
//! - Secure error handling without information leakage
//! - Comprehensive health monitoring
//! - Circuit breaker patterns for reliability
//!
//! ## Healthcare Compliance
//!
//! This application follows healthcare industry standards including:
//! - HIPAA audit trail requirements
//! - Secure data handling practices
//! - Comprehensive error logging
//! - System health monitoring
//! - Data integrity validation

// Core modules
pub mod app;
pub mod commands;
pub mod errors;
pub mod health_checks;
mod infrastructure;
pub mod logging;
pub mod security;

mod state;

// Re-exports for convenience
pub use app::{AppLifecycle, run_app};
pub use errors::{MeditrackError, MeditrackResult, ValidationError};
pub use logging::init_logging;
pub use state::AppState;

/// Main application entry point with comprehensive error handling and security features.
///
/// This function initializes the Meditrack pharmacy management system with:
/// - Secure logging configuration
/// - Comprehensive health checks
/// - Graceful error handling
/// - Healthcare compliance features
/// - Proper resource management
///
/// # Security Features
///
/// - Input validation on all commands
/// - Audit logging for compliance
/// - Secure error handling
/// - Graceful shutdown procedures
///
/// # Healthcare Compliance
///
/// - HIPAA audit trail logging
/// - Data integrity verification
/// - System health monitoring
/// - Secure configuration management
#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // Initialize logging first
    if let Err(e) = init_logging() {
        eprintln!("Failed to initialize logging: {}", e);
        std::process::exit(1);
    }

    if let Err(e) = dotenv::dotenv() {
        tracing::info!("No .env file found: {}", e);
    }

    // Run the application
    if let Err(e) = run_app() {
        tracing::error!("Application failed to run: {}", e);
        std::process::exit(1);
    }
}
