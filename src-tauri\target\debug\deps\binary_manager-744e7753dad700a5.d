D:\programming\desktop-apps\latest-meditrack\meditrack\src-tauri\target\debug\deps\libbinary_manager-744e7753dad700a5.rmeta: crates\binary_manager\src\main.rs Cargo.toml

D:\programming\desktop-apps\latest-meditrack\meditrack\src-tauri\target\debug\deps\binary_manager-744e7753dad700a5.d: crates\binary_manager\src\main.rs Cargo.toml

crates\binary_manager\src\main.rs:
Cargo.toml:

# env-dep:CLIPPY_ARGS=
# env-dep:CLIPPY_CONF_DIR
