{"$message_type": "diagnostic", "message": "unused import: `super::*`", "code": {"code": "unused_imports", "explanation": null}, "level": "warning", "spans": [{"file_name": "src\\infrastructure\\examples.rs", "byte_start": 11942, "byte_end": 11950, "line_start": 361, "line_end": 361, "column_start": 9, "column_end": 17, "is_primary": true, "text": [{"text": "    use super::*;", "highlight_start": 9, "highlight_end": 17}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [{"message": "`#[warn(unused_imports)]` on by default", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "remove the whole `use` item", "code": null, "level": "help", "spans": [{"file_name": "src\\infrastructure\\examples.rs", "byte_start": 11938, "byte_end": 11951, "line_start": 361, "line_end": 361, "column_start": 5, "column_end": 18, "is_primary": true, "text": [{"text": "    use super::*;", "highlight_start": 5, "highlight_end": 18}], "label": null, "suggested_replacement": "", "suggestion_applicability": "MachineApplicable", "expansion": null}], "children": [], "rendered": null}], "rendered": "\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `super::*`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\examples.rs:361:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m361\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    use super::*;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}