{"rustc": 16591470773350601817, "features": "[\"_rt-tokio\", \"_sqlite\", \"bigdecimal\", \"chrono\", \"json\", \"postgres\", \"runtime-tokio\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sqlite\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tls-rustls-ring\", \"tls-rustls-ring-webpki\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_sqlite\", \"_unstable-all-types\", \"all-databases\", \"any\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"default\", \"derive\", \"ipnet\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"regexp\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sqlite\", \"sqlite-preupdate-hook\", \"sqlite-unbundled\", \"sqlx-macros\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tls-native-tls\", \"tls-none\", \"tls-rustls\", \"tls-rustls-aws-lc-rs\", \"tls-rustls-ring\", \"tls-rustls-ring-native-roots\", \"tls-rustls-ring-webpki\", \"uuid\"]", "target": 3003836824758849296, "profile": 2241668132362809309, "path": 14885765977417014357, "deps": [[4783846006802799581, "sqlx_sqlite", false, 3313228266822072696], [16423736323724667376, "sqlx_core", false, 4200359429466598736], [17062459395424751026, "sqlx_postgres", false, 3864978772323322476]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-48d988a5d6fd4929\\dep-lib-sqlx", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}