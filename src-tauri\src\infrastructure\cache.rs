//! Redis cache layer for high-performance data access and session management
//!
//! This module provides a Redis-based caching service for the pharmacy management system,
//! offering high-performance data access, session management, and distributed caching capabilities.

use chrono::{DateTime, Utc};
use redis::{AsyncCommands, Client, Commands, Connection, RedisResult};
use serde::{Deserialize, Serialize, de::DeserializeOwned};
use std::sync::Arc;
use std::time::Duration;
use thiserror::Error;
use tokio::sync::RwLock;
use uuid::Uuid;

/// Errors that can occur in the cache service
#[derive(Error, Debug)]
pub enum CacheError {
    #[error("Redis connection error: {0}")]
    ConnectionError(#[from] redis::RedisError),
    #[error("Serialization error: {0}")]
    SerializationError(#[from] serde_json::Error),
    #[error("Cache key not found: {0}")]
    KeyNotFound(String),
    #[error("Cache operation timeout")]
    Timeout,
    #[error("Invalid cache configuration: {0}")]
    ConfigurationError(String),
}

/// Cache configuration
#[derive(Debug, <PERSON>lone)]
pub struct CacheConfig {
    pub redis_url: String,
    pub default_ttl: Duration,
    pub max_connections: u32,
    pub connection_timeout: Duration,
    pub command_timeout: Duration,
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            redis_url: "redis://localhost:6379".to_string(),
            default_ttl: Duration::from_secs(3600), // 1 hour
            max_connections: 10,
            connection_timeout: Duration::from_secs(5),
            command_timeout: Duration::from_secs(2),
        }
    }
}

/// Cache service for high-performance data access
#[derive(Clone)]
pub struct CacheService {
    client: Client,
    config: CacheConfig,
    connection_pool: Arc<RwLock<Vec<Connection>>>,
}

impl CacheService {
    /// Create a new cache service with the given configuration
    pub async fn new(config: CacheConfig) -> Result<Self, CacheError> {
        let client = Client::open(config.redis_url.as_str())?;

        // Test connection
        let mut conn = client.get_connection()?;
        let _: String = redis::cmd("PING").query(&mut conn)?;

        let service = Self {
            client,
            config,
            connection_pool: Arc::new(RwLock::new(Vec::new())),
        };

        tracing::info!(
            "Cache service initialized with Redis at {}",
            service.config.redis_url
        );
        Ok(service)
    }

    /// Set a value in the cache with default TTL
    pub async fn set<T>(&self, key: &str, value: &T) -> Result<(), CacheError>
    where
        T: Serialize,
    {
        self.set_with_ttl(key, value, self.config.default_ttl).await
    }

    /// Set a value in the cache with custom TTL
    pub async fn set_with_ttl<T>(
        &self,
        key: &str,
        value: &T,
        ttl: Duration,
    ) -> Result<(), CacheError>
    where
        T: Serialize,
    {
        let serialized = serde_json::to_string(value)?;
        let mut conn = self.client.get_async_connection().await?;

        let _: () = conn.set_ex(key, serialized, ttl.as_secs()).await?;

        tracing::debug!("Set cache key '{}' with TTL {:?}", key, ttl);
        Ok(())
    }

    /// Get a value from the cache
    pub async fn get<T>(&self, key: &str) -> Result<Option<T>, CacheError>
    where
        T: DeserializeOwned,
    {
        let mut conn = self.client.get_async_connection().await?;

        let result: Option<String> = conn.get(key).await?;

        match result {
            Some(serialized) => {
                let value = serde_json::from_str(&serialized)?;
                tracing::debug!("Cache hit for key '{}'", key);
                Ok(Some(value))
            }
            None => {
                tracing::debug!("Cache miss for key '{}'", key);
                Ok(None)
            }
        }
    }

    /// Delete a key from the cache
    pub async fn delete(&self, key: &str) -> Result<bool, CacheError> {
        let mut conn = self.client.get_async_connection().await?;
        let deleted: i32 = conn.del(key).await?;

        tracing::debug!("Deleted cache key '{}': {}", key, deleted > 0);
        Ok(deleted > 0)
    }

    /// Check if a key exists in the cache
    pub async fn exists(&self, key: &str) -> Result<bool, CacheError> {
        let mut conn = self.client.get_async_connection().await?;
        let exists: bool = conn.exists(key).await?;

        Ok(exists)
    }

    /// Set expiration time for a key
    pub async fn expire(&self, key: &str, ttl: Duration) -> Result<bool, CacheError> {
        let mut conn = self.client.get_async_connection().await?;
        let result: bool = conn.expire(key, ttl.as_secs() as i64).await?;

        Ok(result)
    }

    /// Get multiple values from the cache
    pub async fn mget<T>(&self, keys: &[&str]) -> Result<Vec<Option<T>>, CacheError>
    where
        T: DeserializeOwned,
    {
        let mut conn = self.client.get_async_connection().await?;
        let results: Vec<Option<String>> = conn.get(keys).await?;

        let mut values = Vec::new();
        for result in results {
            match result {
                Some(serialized) => {
                    let value = serde_json::from_str(&serialized)?;
                    values.push(Some(value));
                }
                None => values.push(None),
            }
        }

        Ok(values)
    }

    /// Set multiple values in the cache
    pub async fn mset<T>(&self, pairs: &[(&str, &T)]) -> Result<(), CacheError>
    where
        T: Serialize,
    {
        let mut conn = self.client.get_async_connection().await?;

        for (key, value) in pairs {
            let serialized = serde_json::to_string(value)?;
            let _: () = conn.set(*key, serialized).await?;
        }

        Ok(())
    }

    /// Increment a numeric value in the cache
    pub async fn increment(&self, key: &str, delta: i64) -> Result<i64, CacheError> {
        let mut conn = self.client.get_async_connection().await?;
        let result: i64 = conn.incr(key, delta).await?;

        Ok(result)
    }

    /// Add item to a list (left push)
    pub async fn list_push<T>(&self, key: &str, value: &T) -> Result<i64, CacheError>
    where
        T: Serialize,
    {
        let serialized = serde_json::to_string(value)?;
        let mut conn = self.client.get_async_connection().await?;
        let length: i64 = conn.lpush(key, serialized).await?;

        Ok(length)
    }

    /// Get items from a list
    pub async fn list_range<T>(
        &self,
        key: &str,
        start: i64,
        stop: i64,
    ) -> Result<Vec<T>, CacheError>
    where
        T: DeserializeOwned,
    {
        let mut conn = self.client.get_async_connection().await?;
        let results: Vec<String> = conn.lrange(key, start as isize, stop as isize).await?;

        let mut values = Vec::new();
        for result in results {
            let value = serde_json::from_str(&result)?;
            values.push(value);
        }

        Ok(values)
    }

    /// Add item to a set
    pub async fn set_add<T>(&self, key: &str, value: &T) -> Result<bool, CacheError>
    where
        T: Serialize,
    {
        let serialized = serde_json::to_string(value)?;
        let mut conn = self.client.get_async_connection().await?;
        let added: i32 = conn.sadd(key, serialized).await?;

        Ok(added > 0)
    }

    /// Get all members of a set
    pub async fn set_members<T>(&self, key: &str) -> Result<Vec<T>, CacheError>
    where
        T: DeserializeOwned,
    {
        let mut conn = self.client.get_async_connection().await?;
        let results: Vec<String> = conn.smembers(key).await?;

        let mut values = Vec::new();
        for result in results {
            let value = serde_json::from_str(&result)?;
            values.push(value);
        }

        Ok(values)
    }

    /// Clear all cache entries (use with caution)
    pub async fn flush_all(&self) -> Result<(), CacheError> {
        let mut conn = self.client.get_async_connection().await?;
        let _: () = redis::cmd("FLUSHALL").query_async(&mut conn).await?;

        tracing::warn!("Flushed all cache entries");
        Ok(())
    }

    /// Get cache statistics
    pub async fn get_stats(&self) -> Result<CacheStats, CacheError> {
        let mut conn = self.client.get_async_connection().await?;
        let info: String = redis::cmd("INFO")
            .arg("memory")
            .query_async(&mut conn)
            .await?;

        // Parse basic stats from Redis INFO command
        let mut stats = CacheStats::default();

        for line in info.lines() {
            if let Some((key, value)) = line.split_once(':') {
                match key {
                    "used_memory" => {
                        if let Ok(memory) = value.parse::<u64>() {
                            stats.memory_used = memory;
                        }
                    }
                    "keyspace_hits" => {
                        if let Ok(hits) = value.parse::<u64>() {
                            stats.cache_hits = hits;
                        }
                    }
                    "keyspace_misses" => {
                        if let Ok(misses) = value.parse::<u64>() {
                            stats.cache_misses = misses;
                        }
                    }
                    _ => {}
                }
            }
        }

        Ok(stats)
    }
}

/// Cache statistics
#[derive(Debug, Default, Serialize, Deserialize)]
pub struct CacheStats {
    pub memory_used: u64,
    pub cache_hits: u64,
    pub cache_misses: u64,
}

impl CacheStats {
    pub fn hit_rate(&self) -> f64 {
        let total = self.cache_hits + self.cache_misses;
        if total == 0 {
            0.0
        } else {
            self.cache_hits as f64 / total as f64
        }
    }
}

/// Session management using cache
pub struct SessionManager {
    cache: CacheService,
    session_ttl: Duration,
}

impl SessionManager {
    pub fn new(cache: CacheService, session_ttl: Duration) -> Self {
        Self { cache, session_ttl }
    }

    /// Create a new session
    pub async fn create_session(
        &self,
        user_id: Uuid,
        session_data: SessionData,
    ) -> Result<String, CacheError> {
        let session_id = Uuid::new_v4().to_string();
        let session_key = format!("session:{}", session_id);

        let session = Session {
            id: session_id.clone(),
            user_id,
            data: session_data,
            created_at: Utc::now(),
            last_accessed: Utc::now(),
        };

        self.cache
            .set_with_ttl(&session_key, &session, self.session_ttl)
            .await?;

        tracing::info!("Created session {} for user {}", session_id, user_id);
        Ok(session_id)
    }

    /// Get session data
    pub async fn get_session(&self, session_id: &str) -> Result<Option<Session>, CacheError> {
        let session_key = format!("session:{}", session_id);
        let mut session: Option<Session> = self.cache.get(&session_key).await?;

        if let Some(ref mut session) = session {
            // Update last accessed time
            session.last_accessed = Utc::now();
            self.cache
                .set_with_ttl(&session_key, session, self.session_ttl)
                .await?;
        }

        Ok(session)
    }

    /// Delete a session
    pub async fn delete_session(&self, session_id: &str) -> Result<bool, CacheError> {
        let session_key = format!("session:{}", session_id);
        let deleted = self.cache.delete(&session_key).await?;

        if deleted {
            tracing::info!("Deleted session {}", session_id);
        }

        Ok(deleted)
    }

    /// Extend session TTL
    pub async fn extend_session(&self, session_id: &str) -> Result<bool, CacheError> {
        let session_key = format!("session:{}", session_id);
        self.cache.expire(&session_key, self.session_ttl).await
    }
}

/// Session data structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Session {
    pub id: String,
    pub user_id: Uuid,
    pub data: SessionData,
    pub created_at: DateTime<Utc>,
    pub last_accessed: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionData {
    pub role: String,
    pub permissions: Vec<String>,
    pub location_id: Option<Uuid>,
    pub preferences: serde_json::Value,
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::sleep;

    #[tokio::test]
    async fn test_cache_basic_operations() {
        let config = CacheConfig::default();
        let cache = CacheService::new(config).await.unwrap();

        // Test set and get
        let test_data = "test_value";
        cache.set("test_key", &test_data).await.unwrap();

        let retrieved: Option<String> = cache.get("test_key").await.unwrap();
        assert_eq!(retrieved, Some(test_data.to_string()));

        // Test delete
        let deleted = cache.delete("test_key").await.unwrap();
        assert!(deleted);

        let retrieved_after_delete: Option<String> = cache.get("test_key").await.unwrap();
        assert_eq!(retrieved_after_delete, None);
    }

    #[tokio::test]
    async fn test_cache_ttl() {
        let config = CacheConfig::default();
        let cache = CacheService::new(config).await.unwrap();

        let test_data = "test_value";
        cache
            .set_with_ttl("ttl_test", &test_data, Duration::from_millis(100))
            .await
            .unwrap();

        // Should exist immediately
        let exists = cache.exists("ttl_test").await.unwrap();
        assert!(exists);

        // Wait for expiration
        sleep(Duration::from_millis(150)).await;

        let exists_after_ttl = cache.exists("ttl_test").await.unwrap();
        assert!(!exists_after_ttl);
    }

    #[tokio::test]
    async fn test_session_management() {
        let config = CacheConfig::default();
        let cache = CacheService::new(config).await.unwrap();
        let session_manager = SessionManager::new(cache, Duration::from_secs(3600));

        let user_id = Uuid::new_v4();
        let session_data = SessionData {
            role: "pharmacist".to_string(),
            permissions: vec!["read".to_string(), "write".to_string()],
            location_id: Some(Uuid::new_v4()),
            preferences: serde_json::json!({"theme": "dark"}),
        };

        // Create session
        let session_id = session_manager
            .create_session(user_id, session_data.clone())
            .await
            .unwrap();

        // Get session
        let session = session_manager.get_session(&session_id).await.unwrap();
        assert!(session.is_some());
        assert_eq!(session.unwrap().user_id, user_id);

        // Delete session
        let deleted = session_manager.delete_session(&session_id).await.unwrap();
        assert!(deleted);

        // Verify session is gone
        let session_after_delete = session_manager.get_session(&session_id).await.unwrap();
        assert!(session_after_delete.is_none());
    }
}
