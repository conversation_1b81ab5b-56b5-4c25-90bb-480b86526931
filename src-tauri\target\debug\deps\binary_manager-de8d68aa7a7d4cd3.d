D:\programming\desktop-apps\latest-meditrack\meditrack\src-tauri\target\debug\deps\libbinary_manager-de8d68aa7a7d4cd3.rmeta: crates\binary_manager\src\lib.rs Cargo.toml

D:\programming\desktop-apps\latest-meditrack\meditrack\src-tauri\target\debug\deps\binary_manager-de8d68aa7a7d4cd3.d: crates\binary_manager\src\lib.rs Cargo.toml

crates\binary_manager\src\lib.rs:
Cargo.toml:

# env-dep:CLIPPY_ARGS=
# env-dep:CLIPPY_CONF_DIR
