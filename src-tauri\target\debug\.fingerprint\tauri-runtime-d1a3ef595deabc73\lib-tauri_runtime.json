{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2241668132362809309, "path": 18316066009122804046, "deps": [[442785307232013896, "build_script_build", false, 1315052582861037539], [3150220818285335163, "url", false, 3569982634382302943], [4143744114649553716, "raw_window_handle", false, 8084507311091100797], [7606335748176206944, "dpi", false, 6013810726208640132], [9010263965687315507, "http", false, 15283923244554771932], [9689903380558560274, "serde", false, 1190822165864209379], [10806645703491011684, "thiserror", false, 3797447290076662653], [11050281405049894993, "tauri_utils", false, 5803446630889171054], [13116089016666501665, "windows", false, 6388814616047702111], [15367738274754116744, "serde_json", false, 13370400617386028636], [16727543399706004146, "cookie", false, 2163608195938603892]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-d1a3ef595deabc73\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}