[workspace]
members = [".", "db/*", "crates/*"]
resolver = "2"

[package]
name = "app"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "app_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2.2.0", features = [] }

[dependencies]
chrono = { workspace = true }
dotenv = { workspace = true }
regex = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
async-trait = { workspace = true }
lettre = { workspace = true }
redis = { workspace = true, features = ["aio", "tokio-comp"] }
rust_decimal = { workspace = true }
sea-orm-migration = { workspace = true }
sea-orm = { workspace = true, features = [
    "mock",
    "sqlx-postgres",
    "sqlx-sqlite",
    "runtime-tokio-rustls",
] }
sha2 = { workspace = true }
tauri = { version = "2.5.1", features = [] }
tauri-plugin-opener = "2.2.7"
thiserror = { workspace = true }
tokio = { workspace = true, features = ["full"] }
toml = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true, features = ["env-filter", "json"] }
url = { workspace = true }
uuid = { workspace = true }

app_config = { workspace = true }
db_entity = { workspace = true }
db_migration = { workspace = true }
db_service = { workspace = true }

[workspace.dependencies]
argon2 = "0.5.3"
async-recursion = "1.1.1"
async-trait = "0.1"
chrono = "0.4.40"
clap = "4.4"
config = "0.15.11"
derive_more = "2.0.1"
derive-getters = "0.5"
dotenv = "0.15.0"
jsonwebtoken = "9.3.1"
lettre = { version = "0.11.10", features = [
    "smtp-transport",
    "tokio1",
    "tokio1-native-tls",
    "builder",
    "hostname",
    "tracing",
] }
once_cell = "1.21.3"
pretty_assertions = "1.4.1"
redis = { version = "0.27.5", features = ["aio", "tokio-comp"] }
regex = "1"
rust_decimal = "1.37.1"
sea-orm = "1"
sea-orm-migration = "1"
serde = "1.0"
serde_json = "1.0"
sha2 = "0.10.8"
tempfile = "3.8"
thiserror = "2"
tokio = "1.45.1"
toml = "0.8.8"
tracing = "0.1"
tracing-subscriber = "0.3"
typed-builder = "0.21.0"
url = "2"
uuid = { version = "1.17.0", features = ["v4"] }

app_config = { path = "crates/config" }
db_entity = { path = "db/entity" }
db_migration = { path = "db/migration" }
db_service = { path = "db/service" }
