{"$message_type":"diagnostic","message":"the name `EventStats` is defined multiple times","code":{"code":"E0428","explanation":"A type or module has been defined more than once.\n\nErroneous code example:\n\n```compile_fail,E0428\nstruct Bar;\nstruct Bar; // error: duplicate definition of value `Bar`\n```\n\nPlease verify you didn't misspell the type/module's name or remove/rename the\nduplicated one. Example:\n\n```\nstruct Bar;\nstruct Bar2; // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\event_bus.rs","byte_start":7827,"byte_end":7848,"line_start":257,"line_end":257,"column_start":1,"column_end":22,"is_primary":true,"text":[{"text":"pub struct EventStats {","highlight_start":1,"highlight_end":22}],"label":"`EventStats` redefined here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\event_bus.rs","byte_start":3692,"byte_end":3709,"line_start":141,"line_end":141,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"struct EventStats {","highlight_start":1,"highlight_end":18}],"label":"previous definition of the type `EventStats` here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`EventStats` must be defined only once in the type namespace of this module","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0428]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the name `EventStats` is defined multiple times\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\event_bus.rs:257:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m141\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mstruct EventStats {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mprevious definition of the type `EventStats` here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m257\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct EventStats {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m`EventStats` redefined here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `EventStats` must be defined only once in the type namespace of this module\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unresolved import `redis::AsyncCommands`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":322,"byte_end":335,"line_start":7,"line_end":7,"column_start":13,"column_end":26,"is_primary":true,"text":[{"text":"use redis::{AsyncCommands, Client, Commands, Connection, RedisResult};","highlight_start":13,"highlight_end":26}],"label":"no `AsyncCommands` in the root","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"found an item that was configured out","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\redis-0.27.6\\src\\lib.rs","byte_start":20352,"byte_end":20365,"line_start":573,"line_end":573,"column_start":31,"column_end":44,"is_primary":true,"text":[{"text":"    cmd::AsyncIter, commands::AsyncCommands, parser::parse_redis_value_async, types::RedisFuture,","highlight_start":31,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"the item is gated behind the `aio` feature","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\redis-0.27.6\\src\\lib.rs","byte_start":20240,"byte_end":20255,"line_start":570,"line_end":570,"column_start":7,"column_end":22,"is_primary":true,"text":[{"text":"#[cfg(feature = \"aio\")]","highlight_start":7,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unresolved import `redis::AsyncCommands`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:7:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse redis::{AsyncCommands, Client, Commands, Connection, RedisResult};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `AsyncCommands` in the root\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: found an item that was configured out\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\redis-0.27.6\\src\\lib.rs:573:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m573\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    cmd::AsyncIter, commands::AsyncCommands, parser::parse_redis_value_async, types::RedisFuture,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: the item is gated behind the `aio` feature\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\redis-0.27.6\\src\\lib.rs:570:7\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m570\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[cfg(feature = \"aio\")]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unresolved imports `lettre::AsyncTransport`, `lettre::AsyncSmtpTransport`, `lettre::Tokio1Executor`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\notifications.rs","byte_start":475,"byte_end":489,"line_start":15,"line_end":15,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    AsyncTransport, AsyncSmtpTransport, Tokio1Executor,","highlight_start":5,"highlight_end":19}],"label":"no `AsyncTransport` in the root","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\notifications.rs","byte_start":491,"byte_end":509,"line_start":15,"line_end":15,"column_start":21,"column_end":39,"is_primary":true,"text":[{"text":"    AsyncTransport, AsyncSmtpTransport, Tokio1Executor,","highlight_start":21,"highlight_end":39}],"label":"no `AsyncSmtpTransport` in the root","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\notifications.rs","byte_start":511,"byte_end":525,"line_start":15,"line_end":15,"column_start":41,"column_end":55,"is_primary":true,"text":[{"text":"    AsyncTransport, AsyncSmtpTransport, Tokio1Executor,","highlight_start":41,"highlight_end":55}],"label":"no `Tokio1Executor` in the root","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"found an item that was configured out","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\lettre-0.11.18\\src\\lib.rs","byte_start":12583,"byte_end":12597,"line_start":291,"line_end":291,"column_start":26,"column_end":40,"is_primary":true,"text":[{"text":"pub use self::transport::AsyncTransport;","highlight_start":26,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"the item is gated here","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\lettre-0.11.18\\src\\lib.rs","byte_start":12487,"byte_end":12542,"line_start":289,"line_end":289,"column_start":1,"column_end":56,"is_primary":true,"text":[{"text":"#[cfg(any(feature = \"tokio1\", feature = \"async-std1\"))]","highlight_start":1,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"found an item that was configured out","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\lettre-0.11.18\\src\\lib.rs","byte_start":13389,"byte_end":13407,"line_start":318,"line_end":318,"column_start":33,"column_end":51,"is_primary":true,"text":[{"text":"pub use crate::transport::smtp::AsyncSmtpTransport;","highlight_start":33,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"the item is gated here","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\lettre-0.11.18\\src\\lib.rs","byte_start":13258,"byte_end":13356,"line_start":314,"line_end":317,"column_start":1,"column_end":4,"is_primary":true,"text":[{"text":"#[cfg(all(","highlight_start":1,"highlight_end":11},{"text":"    feature = \"smtp-transport\",","highlight_start":1,"highlight_end":32},{"text":"    any(feature = \"tokio1\", feature = \"async-std1\")","highlight_start":1,"highlight_end":52},{"text":"))]","highlight_start":1,"highlight_end":4}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"found an item that was configured out","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\lettre-0.11.18\\src\\lib.rs","byte_start":12471,"byte_end":12485,"line_start":288,"line_end":288,"column_start":25,"column_end":39,"is_primary":true,"text":[{"text":"pub use self::executor::Tokio1Executor;","highlight_start":25,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"the item is gated behind the `tokio1` feature","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\lettre-0.11.18\\src\\lib.rs","byte_start":12426,"byte_end":12444,"line_start":287,"line_end":287,"column_start":7,"column_end":25,"is_primary":true,"text":[{"text":"#[cfg(feature = \"tokio1\")]","highlight_start":7,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"a similar name exists in the module","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\notifications.rs","byte_start":491,"byte_end":509,"line_start":15,"line_end":15,"column_start":21,"column_end":39,"is_primary":true,"text":[{"text":"    AsyncTransport, AsyncSmtpTransport, Tokio1Executor,","highlight_start":21,"highlight_end":39}],"label":null,"suggested_replacement":"SmtpTransport","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unresolved imports `lettre::AsyncTransport`, `lettre::AsyncSmtpTransport`, `lettre::Tokio1Executor`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\notifications.rs:15:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    AsyncTransport, AsyncSmtpTransport, Tokio1Executor,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `Tokio1Executor` in the root\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `AsyncSmtpTransport` in the root\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mhelp: a similar name exists in the module: `SmtpTransport`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `AsyncTransport` in the root\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: found an item that was configured out\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\lettre-0.11.18\\src\\lib.rs:291:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m291\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use self::transport::AsyncTransport;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: the item is gated here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\lettre-0.11.18\\src\\lib.rs:289:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m289\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[cfg(any(feature = \"tokio1\", feature = \"async-std1\"))]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: found an item that was configured out\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\lettre-0.11.18\\src\\lib.rs:318:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m318\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::transport::smtp::AsyncSmtpTransport;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: the item is gated here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\lettre-0.11.18\\src\\lib.rs:314:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m314\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[cfg(all(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m315\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    feature = \"smtp-transport\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m316\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    any(feature = \"tokio1\", feature = \"async-std1\")\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m317\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m))]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|___^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: found an item that was configured out\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\lettre-0.11.18\\src\\lib.rs:288:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m288\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use self::executor::Tokio1Executor;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: the item is gated behind the `tokio1` feature\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\lettre-0.11.18\\src\\lib.rs:287:7\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m287\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[cfg(feature = \"tokio1\")]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unresolved import `super::NotificationConfig`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\core.rs","byte_start":443,"byte_end":461,"line_start":14,"line_end":14,"column_start":63,"column_end":81,"is_primary":true,"text":[{"text":"    EventBus, CacheService, CacheConfig, NotificationService, NotificationConfig,","highlight_start":63,"highlight_end":81}],"label":"no `NotificationConfig` in `infrastructure`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing this struct instead:\ncrate::infrastructure::notifications::NotificationConfig","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"a similar name exists in the module","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\core.rs","byte_start":443,"byte_end":461,"line_start":14,"line_end":14,"column_start":63,"column_end":81,"is_primary":true,"text":[{"text":"    EventBus, CacheService, CacheConfig, NotificationService, NotificationConfig,","highlight_start":63,"highlight_end":81}],"label":null,"suggested_replacement":"NotificationChannel","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unresolved import `super::NotificationConfig`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\core.rs:14:63\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    EventBus, CacheService, CacheConfig, NotificationService, NotificationConfig,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `NotificationConfig` in `infrastructure`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mhelp: a similar name exists in the module: `NotificationChannel`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider importing this struct instead:\u001b[0m\n\u001b[0m           crate::infrastructure::notifications::NotificationConfig\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unresolved imports `super::AlertSeverity`, `super::SchedulePriority`, `super::TransactionType`, `super::NotificationPriority`, `super::NotificationType`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":349,"byte_end":362,"line_start":12,"line_end":12,"column_start":20,"column_end":33,"is_primary":true,"text":[{"text":"    PharmacyEvent, AlertSeverity, SchedulePriority, TransactionType,","highlight_start":20,"highlight_end":33}],"label":"no `AlertSeverity` in `infrastructure`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\examples.rs","byte_start":364,"byte_end":380,"line_start":12,"line_end":12,"column_start":35,"column_end":51,"is_primary":true,"text":[{"text":"    PharmacyEvent, AlertSeverity, SchedulePriority, TransactionType,","highlight_start":35,"highlight_end":51}],"label":"no `SchedulePriority` in `infrastructure`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\examples.rs","byte_start":382,"byte_end":397,"line_start":12,"line_end":12,"column_start":53,"column_end":68,"is_primary":true,"text":[{"text":"    PharmacyEvent, AlertSeverity, SchedulePriority, TransactionType,","highlight_start":53,"highlight_end":68}],"label":"no `TransactionType` in `infrastructure`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\examples.rs","byte_start":482,"byte_end":502,"line_start":14,"line_end":14,"column_start":47,"column_end":67,"is_primary":true,"text":[{"text":"    NotificationMessage, NotificationChannel, NotificationPriority, NotificationType,","highlight_start":47,"highlight_end":67}],"label":"no `NotificationPriority` in `infrastructure`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\examples.rs","byte_start":504,"byte_end":520,"line_start":14,"line_end":14,"column_start":69,"column_end":85,"is_primary":true,"text":[{"text":"    NotificationMessage, NotificationChannel, NotificationPriority, NotificationType,","highlight_start":69,"highlight_end":85}],"label":"no `NotificationType` in `infrastructure`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing this enum instead:\ncrate::infrastructure::event_bus::AlertSeverity","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"consider importing this enum instead:\ncrate::infrastructure::event_bus::SchedulePriority","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"consider importing this enum instead:\ncrate::infrastructure::event_bus::TransactionType","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"consider importing this enum instead:\ncrate::infrastructure::notifications::NotificationPriority","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"consider importing this enum instead:\ncrate::infrastructure::notifications::NotificationType","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"a similar name exists in the module","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":349,"byte_end":362,"line_start":12,"line_end":12,"column_start":20,"column_end":33,"is_primary":true,"text":[{"text":"    PharmacyEvent, AlertSeverity, SchedulePriority, TransactionType,","highlight_start":20,"highlight_end":33}],"label":null,"suggested_replacement":"AuditSeverity","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"a similar name exists in the module","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":482,"byte_end":502,"line_start":14,"line_end":14,"column_start":47,"column_end":67,"is_primary":true,"text":[{"text":"    NotificationMessage, NotificationChannel, NotificationPriority, NotificationType,","highlight_start":47,"highlight_end":67}],"label":null,"suggested_replacement":"NotificationError","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"a similar name exists in the module","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":504,"byte_end":520,"line_start":14,"line_end":14,"column_start":69,"column_end":85,"is_primary":true,"text":[{"text":"    NotificationMessage, NotificationChannel, NotificationPriority, NotificationType,","highlight_start":69,"highlight_end":85}],"label":null,"suggested_replacement":"notifications","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unresolved imports `super::AlertSeverity`, `super::SchedulePriority`, `super::TransactionType`, `super::NotificationPriority`, `super::NotificationType`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\examples.rs:12:20\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    PharmacyEvent, AlertSeverity, SchedulePriority, TransactionType,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `TransactionType` in `infrastructure`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `SchedulePriority` in `infrastructure`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `AlertSeverity` in `infrastructure`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    AuditEventType, AuditSeverity,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    NotificationMessage, NotificationChannel, NotificationPriority, NotificationType,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `NotificationType` in `infrastructure`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `NotificationPriority` in `infrastructure`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider importing this enum instead:\u001b[0m\n\u001b[0m           crate::infrastructure::event_bus::AlertSeverity\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider importing this enum instead:\u001b[0m\n\u001b[0m           crate::infrastructure::event_bus::SchedulePriority\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider importing this enum instead:\u001b[0m\n\u001b[0m           crate::infrastructure::event_bus::TransactionType\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider importing this enum instead:\u001b[0m\n\u001b[0m           crate::infrastructure::notifications::NotificationPriority\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider importing this enum instead:\u001b[0m\n\u001b[0m           crate::infrastructure::notifications::NotificationType\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: a similar name exists in the module\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    PharmacyEvent, \u001b[0m\u001b[0m\u001b[38;5;9mAlertSeverity\u001b[0m\u001b[0m, SchedulePriority, TransactionType,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    PharmacyEvent, \u001b[0m\u001b[0m\u001b[38;5;10mAuditSeverity\u001b[0m\u001b[0m, SchedulePriority, TransactionType,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: a similar name exists in the module\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    NotificationMessage, NotificationChannel, \u001b[0m\u001b[0m\u001b[38;5;9mNotificationPriority\u001b[0m\u001b[0m, NotificationType,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    NotificationMessage, NotificationChannel, \u001b[0m\u001b[0m\u001b[38;5;10mNotificationError\u001b[0m\u001b[0m, NotificationType,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: a similar name exists in the module\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    NotificationMessage, NotificationChannel, NotificationPriority, \u001b[0m\u001b[0m\u001b[38;5;9mNotificationType\u001b[0m\u001b[0m,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    NotificationMessage, NotificationChannel, NotificationPriority, \u001b[0m\u001b[0m\u001b[38;5;10mnotifications\u001b[0m\u001b[0m,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing lifetime specifier","code":{"code":"E0106","explanation":"This error indicates that a lifetime is missing from a type. If it is an error\ninside a function signature, the problem may be with failing to adhere to the\nlifetime elision rules (see below).\n\nErroneous code examples:\n\n```compile_fail,E0106\nstruct Foo1 { x: &bool }\n              // ^ expected lifetime parameter\nstruct Foo2<'a> { x: &'a bool } // correct\n\nstruct Bar1 { x: Foo2 }\n              // ^^^^ expected lifetime parameter\nstruct Bar2<'a> { x: Foo2<'a> } // correct\n\nenum Baz1 { A(u8), B(&bool), }\n                  // ^ expected lifetime parameter\nenum Baz2<'a> { A(u8), B(&'a bool), } // correct\n\ntype MyStr1 = &str;\n           // ^ expected lifetime parameter\ntype MyStr2<'a> = &'a str; // correct\n```\n\nLifetime elision is a special, limited kind of inference for lifetimes in\nfunction signatures which allows you to leave out lifetimes in certain cases.\nFor more background on lifetime elision see [the book][book-le].\n\nThe lifetime elision rules require that any function signature with an elided\noutput lifetime must either have:\n\n - exactly one input lifetime\n - or, multiple input lifetimes, but the function must also be a method with a\n   `&self` or `&mut self` receiver\n\nIn the first case, the output lifetime is inferred to be the same as the unique\ninput lifetime. In the second case, the lifetime is instead inferred to be the\nsame as the lifetime on `&self` or `&mut self`.\n\nHere are some examples of elision errors:\n\n```compile_fail,E0106\n// error, no input lifetimes\nfn foo() -> &str { }\n\n// error, `x` and `y` have distinct lifetimes inferred\nfn bar(x: &str, y: &str) -> &str { }\n\n// error, `y`'s lifetime is inferred to be distinct from `x`'s\nfn baz<'a>(x: &'a str, y: &str) -> &str { }\n```\n\n[book-le]: https://doc.rust-lang.org/book/ch10-03-lifetime-syntax.html#lifetime-elision\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\database.rs","byte_start":9019,"byte_end":9021,"line_start":270,"line_end":270,"column_start":106,"column_end":108,"is_primary":true,"text":[{"text":"        F: Fn() -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<R, DatabaseError>> + Send + '_>>,","highlight_start":106,"highlight_end":108}],"label":"expected named lifetime parameter","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"this function's return type contains a borrowed value, but there is no value for it to be borrowed from","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"consider using the `'static` lifetime, but this is uncommon unless you're returning a borrowed value from a `const` or a `static`, or if you will only have owned values","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\database.rs","byte_start":9019,"byte_end":9021,"line_start":270,"line_end":270,"column_start":106,"column_end":108,"is_primary":true,"text":[{"text":"        F: Fn() -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<R, DatabaseError>> + Send + '_>>,","highlight_start":106,"highlight_end":108}],"label":null,"suggested_replacement":"'static","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0106]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing lifetime specifier\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\database.rs:270:106\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m270\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        F: Fn() -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<R, DatabaseError>> + Send + '_>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected named lifetime parameter\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: this function's return type contains a borrowed value, but there is no value for it to be borrowed from\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider using the `'static` lifetime, but this is uncommon unless you're returning a borrowed value from a `const` or a `static`, or if you will only have owned values\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m270\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        F: Fn() -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<R, DatabaseError>> + Send + \u001b[0m\u001b[0m\u001b[38;5;9m'_\u001b[0m\u001b[0m>>,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m270\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        F: Fn() -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<R, DatabaseError>> + Send + \u001b[0m\u001b[0m\u001b[38;5;10m'static\u001b[0m\u001b[0m>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing lifetime specifier","code":{"code":"E0106","explanation":"This error indicates that a lifetime is missing from a type. If it is an error\ninside a function signature, the problem may be with failing to adhere to the\nlifetime elision rules (see below).\n\nErroneous code examples:\n\n```compile_fail,E0106\nstruct Foo1 { x: &bool }\n              // ^ expected lifetime parameter\nstruct Foo2<'a> { x: &'a bool } // correct\n\nstruct Bar1 { x: Foo2 }\n              // ^^^^ expected lifetime parameter\nstruct Bar2<'a> { x: Foo2<'a> } // correct\n\nenum Baz1 { A(u8), B(&bool), }\n                  // ^ expected lifetime parameter\nenum Baz2<'a> { A(u8), B(&'a bool), } // correct\n\ntype MyStr1 = &str;\n           // ^ expected lifetime parameter\ntype MyStr2<'a> = &'a str; // correct\n```\n\nLifetime elision is a special, limited kind of inference for lifetimes in\nfunction signatures which allows you to leave out lifetimes in certain cases.\nFor more background on lifetime elision see [the book][book-le].\n\nThe lifetime elision rules require that any function signature with an elided\noutput lifetime must either have:\n\n - exactly one input lifetime\n - or, multiple input lifetimes, but the function must also be a method with a\n   `&self` or `&mut self` receiver\n\nIn the first case, the output lifetime is inferred to be the same as the unique\ninput lifetime. In the second case, the lifetime is instead inferred to be the\nsame as the lifetime on `&self` or `&mut self`.\n\nHere are some examples of elision errors:\n\n```compile_fail,E0106\n// error, no input lifetimes\nfn foo() -> &str { }\n\n// error, `x` and `y` have distinct lifetimes inferred\nfn bar(x: &str, y: &str) -> &str { }\n\n// error, `y`'s lifetime is inferred to be distinct from `x`'s\nfn baz<'a>(x: &'a str, y: &str) -> &str { }\n```\n\n[book-le]: https://doc.rust-lang.org/book/ch10-03-lifetime-syntax.html#lifetime-elision\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\database.rs","byte_start":12641,"byte_end":12643,"line_start":369,"line_end":369,"column_start":106,"column_end":108,"is_primary":true,"text":[{"text":"        F: Fn() -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<R, DatabaseError>> + Send + '_>>,","highlight_start":106,"highlight_end":108}],"label":"expected named lifetime parameter","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"this function's return type contains a borrowed value, but there is no value for it to be borrowed from","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"consider using the `'static` lifetime, but this is uncommon unless you're returning a borrowed value from a `const` or a `static`, or if you will only have owned values","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\database.rs","byte_start":12641,"byte_end":12643,"line_start":369,"line_end":369,"column_start":106,"column_end":108,"is_primary":true,"text":[{"text":"        F: Fn() -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<R, DatabaseError>> + Send + '_>>,","highlight_start":106,"highlight_end":108}],"label":null,"suggested_replacement":"'static","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0106]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing lifetime specifier\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\database.rs:369:106\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m369\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        F: Fn() -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<R, DatabaseError>> + Send + '_>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected named lifetime parameter\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: this function's return type contains a borrowed value, but there is no value for it to be borrowed from\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider using the `'static` lifetime, but this is uncommon unless you're returning a borrowed value from a `const` or a `static`, or if you will only have owned values\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m369\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        F: Fn() -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<R, DatabaseError>> + Send + \u001b[0m\u001b[0m\u001b[38;5;9m'_\u001b[0m\u001b[0m>>,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m369\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        F: Fn() -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<R, DatabaseError>> + Send + \u001b[0m\u001b[0m\u001b[38;5;10m'static\u001b[0m\u001b[0m>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: could not find `HealthStatus` in `super`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\core.rs","byte_start":7445,"byte_end":7457,"line_start":198,"line_end":198,"column_start":32,"column_end":44,"is_primary":true,"text":[{"text":"                        super::HealthStatus::Healthy","highlight_start":32,"highlight_end":44}],"label":"could not find `HealthStatus` in `super`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing one of these enums","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\core.rs","byte_start":210,"byte_end":210,"line_start":6,"line_end":6,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::sync::Arc;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use crate::infrastructure::event_bus::HealthStatus;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\infrastructure\\core.rs","byte_start":210,"byte_end":210,"line_start":6,"line_end":6,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::sync::Arc;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use crate::state::HealthStatus;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"if you import `HealthStatus`, refer to it directly","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\core.rs","byte_start":7438,"byte_end":7445,"line_start":198,"line_end":198,"column_start":25,"column_end":32,"is_primary":true,"text":[{"text":"                        super::HealthStatus::Healthy","highlight_start":25,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: failed to resolve: could not find `HealthStatus` in `super`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\core.rs:198:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m198\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        super::HealthStatus::Healthy\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcould not find `HealthStatus` in `super`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing one of these enums\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use crate::infrastructure::event_bus::HealthStatus;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use crate::state::HealthStatus;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if you import `HealthStatus`, refer to it directly\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m198\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[38;5;9msuper::\u001b[0m\u001b[0mHealthStatus::Healthy\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m198\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                        HealthStatus::Healthy\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: could not find `HealthStatus` in `super`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\core.rs","byte_start":7529,"byte_end":7541,"line_start":200,"line_end":200,"column_start":32,"column_end":44,"is_primary":true,"text":[{"text":"                        super::HealthStatus::Warning","highlight_start":32,"highlight_end":44}],"label":"could not find `HealthStatus` in `super`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing one of these enums","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\core.rs","byte_start":210,"byte_end":210,"line_start":6,"line_end":6,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::sync::Arc;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use crate::infrastructure::event_bus::HealthStatus;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\infrastructure\\core.rs","byte_start":210,"byte_end":210,"line_start":6,"line_end":6,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::sync::Arc;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use crate::state::HealthStatus;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"if you import `HealthStatus`, refer to it directly","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\core.rs","byte_start":7522,"byte_end":7529,"line_start":200,"line_end":200,"column_start":25,"column_end":32,"is_primary":true,"text":[{"text":"                        super::HealthStatus::Warning","highlight_start":25,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: failed to resolve: could not find `HealthStatus` in `super`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\core.rs:200:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m200\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        super::HealthStatus::Warning\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcould not find `HealthStatus` in `super`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing one of these enums\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use crate::infrastructure::event_bus::HealthStatus;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use crate::state::HealthStatus;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if you import `HealthStatus`, refer to it directly\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m200\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[38;5;9msuper::\u001b[0m\u001b[0mHealthStatus::Warning\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m200\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                        HealthStatus::Warning\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `RedisResult`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":367,"byte_end":378,"line_start":7,"line_end":7,"column_start":58,"column_end":69,"is_primary":true,"text":[{"text":"use redis::{AsyncCommands, Client, Commands, Connection, RedisResult};","highlight_start":58,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":365,"byte_end":378,"line_start":7,"line_end":7,"column_start":56,"column_end":69,"is_primary":true,"text":[{"text":"use redis::{AsyncCommands, Client, Commands, Connection, RedisResult};","highlight_start":56,"highlight_end":69}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `RedisResult`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:7:58\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse redis::{AsyncCommands, Client, Commands, Connection, RedisResult};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Set`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\audit.rs","byte_start":379,"byte_end":382,"line_start":7,"line_end":7,"column_start":76,"column_end":79,"is_primary":true,"text":[{"text":"use sea_orm::{ActiveModelTrait, Database, DatabaseConnection, EntityTrait, Set};","highlight_start":76,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\audit.rs","byte_start":377,"byte_end":382,"line_start":7,"line_end":7,"column_start":74,"column_end":79,"is_primary":true,"text":[{"text":"use sea_orm::{ActiveModelTrait, Database, DatabaseConnection, EntityTrait, Set};","highlight_start":74,"highlight_end":79}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Set`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\audit.rs:7:76\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse sea_orm::{ActiveModelTrait, Database, DatabaseConnection, EntityTrait, Set};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `ConnectionTrait`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\database.rs","byte_start":444,"byte_end":459,"line_start":11,"line_end":11,"column_start":28,"column_end":43,"is_primary":true,"text":[{"text":"    ConnectOptions, DbErr, ConnectionTrait,","highlight_start":28,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\database.rs","byte_start":442,"byte_end":459,"line_start":11,"line_end":11,"column_start":26,"column_end":43,"is_primary":true,"text":[{"text":"    ConnectOptions, DbErr, ConnectionTrait,","highlight_start":26,"highlight_end":43}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `ConnectionTrait`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\database.rs:11:28\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ConnectOptions, DbErr, ConnectionTrait,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `uuid::Uuid`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\database.rs","byte_start":531,"byte_end":541,"line_start":15,"line_end":15,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"use uuid::Uuid;","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\database.rs","byte_start":527,"byte_end":544,"line_start":15,"line_end":16,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use uuid::Uuid;","highlight_start":1,"highlight_end":16},{"text":"use chrono::{DateTime, Utc};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `uuid::Uuid`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\database.rs:15:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse uuid::Uuid;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `uuid::Uuid`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\core.rs","byte_start":322,"byte_end":332,"line_start":10,"line_end":10,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"use uuid::Uuid;","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\core.rs","byte_start":318,"byte_end":335,"line_start":10,"line_end":11,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use uuid::Uuid;","highlight_start":1,"highlight_end":16},{"text":"use chrono::{DateTime, Utc};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `uuid::Uuid`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\core.rs:10:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse uuid::Uuid;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `AuditSeverity`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":420,"byte_end":433,"line_start":13,"line_end":13,"column_start":21,"column_end":34,"is_primary":true,"text":[{"text":"    AuditEventType, AuditSeverity,","highlight_start":21,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":418,"byte_end":433,"line_start":13,"line_end":13,"column_start":19,"column_end":34,"is_primary":true,"text":[{"text":"    AuditEventType, AuditSeverity,","highlight_start":19,"highlight_end":34}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `AuditSeverity`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\examples.rs:13:21\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    AuditEventType, AuditSeverity,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `super::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":11146,"byte_end":11154,"line_start":302,"line_end":302,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    use super::*;","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":11142,"byte_end":11155,"line_start":302,"line_end":302,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    use super::*;","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `super::*`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\examples.rs:302:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m302\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    use super::*;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `SessionManager`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\mod.rs","byte_start":702,"byte_end":716,"line_start":19,"line_end":19,"column_start":56,"column_end":70,"is_primary":true,"text":[{"text":"pub use cache::{CacheService, CacheError, CacheConfig, SessionManager};","highlight_start":56,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\mod.rs","byte_start":700,"byte_end":716,"line_start":19,"line_end":19,"column_start":54,"column_end":70,"is_primary":true,"text":[{"text":"pub use cache::{CacheService, CacheError, CacheConfig, SessionManager};","highlight_start":54,"highlight_end":70}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `SessionManager`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\mod.rs:19:56\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use cache::{CacheService, CacheError, CacheConfig, SessionManager};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `EmailProvider`, `NotificationProvider`, `PushProvider`, and `SmsProvider`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\mod.rs","byte_start":860,"byte_end":880,"line_start":22,"line_end":22,"column_start":28,"column_end":48,"is_primary":true,"text":[{"text":"    NotificationRecipient, NotificationProvider, EmailProvider, SmsProvider, PushProvider","highlight_start":28,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\mod.rs","byte_start":882,"byte_end":895,"line_start":22,"line_end":22,"column_start":50,"column_end":63,"is_primary":true,"text":[{"text":"    NotificationRecipient, NotificationProvider, EmailProvider, SmsProvider, PushProvider","highlight_start":50,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\mod.rs","byte_start":897,"byte_end":908,"line_start":22,"line_end":22,"column_start":65,"column_end":76,"is_primary":true,"text":[{"text":"    NotificationRecipient, NotificationProvider, EmailProvider, SmsProvider, PushProvider","highlight_start":65,"highlight_end":76}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\mod.rs","byte_start":910,"byte_end":922,"line_start":22,"line_end":22,"column_start":78,"column_end":90,"is_primary":true,"text":[{"text":"    NotificationRecipient, NotificationProvider, EmailProvider, SmsProvider, PushProvider","highlight_start":78,"highlight_end":90}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\mod.rs","byte_start":858,"byte_end":922,"line_start":22,"line_end":22,"column_start":26,"column_end":90,"is_primary":true,"text":[{"text":"    NotificationRecipient, NotificationProvider, EmailProvider, SmsProvider, PushProvider","highlight_start":26,"highlight_end":90}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `EmailProvider`, `NotificationProvider`, `PushProvider`, and `SmsProvider`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\mod.rs:22:28\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    NotificationRecipient, NotificationProvider, EmailProvider, SmsProvider, PushProvider\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ComplianceReportType` and `ComplianceReport`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\mod.rs","byte_start":1042,"byte_end":1058,"line_start":26,"line_end":26,"column_start":18,"column_end":34,"is_primary":true,"text":[{"text":"    AuditConfig, ComplianceReport, ComplianceReportType","highlight_start":18,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\mod.rs","byte_start":1060,"byte_end":1080,"line_start":26,"line_end":26,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"    AuditConfig, ComplianceReport, ComplianceReportType","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\mod.rs","byte_start":1040,"byte_end":1080,"line_start":26,"line_end":26,"column_start":16,"column_end":56,"is_primary":true,"text":[{"text":"    AuditConfig, ComplianceReport, ComplianceReportType","highlight_start":16,"highlight_end":56}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `ComplianceReportType` and `ComplianceReport`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\mod.rs:26:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    AuditConfig, ComplianceReport, ComplianceReportType\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ConnectionPool`, `DatabaseStats`, `MigrationManager`, and `TransactionManager`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\mod.rs","byte_start":1128,"byte_end":1142,"line_start":29,"line_end":29,"column_start":22,"column_end":36,"is_primary":true,"text":[{"text":"    DatabaseManager, ConnectionPool, TransactionManager, DatabaseError, DatabaseConfig,","highlight_start":22,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\mod.rs","byte_start":1144,"byte_end":1162,"line_start":29,"line_end":29,"column_start":38,"column_end":56,"is_primary":true,"text":[{"text":"    DatabaseManager, ConnectionPool, TransactionManager, DatabaseError, DatabaseConfig,","highlight_start":38,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\mod.rs","byte_start":1200,"byte_end":1213,"line_start":30,"line_end":30,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    DatabaseStats, MigrationManager","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\mod.rs","byte_start":1215,"byte_end":1231,"line_start":30,"line_end":30,"column_start":20,"column_end":36,"is_primary":true,"text":[{"text":"    DatabaseStats, MigrationManager","highlight_start":20,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\mod.rs","byte_start":1126,"byte_end":1162,"line_start":29,"line_end":29,"column_start":20,"column_end":56,"is_primary":true,"text":[{"text":"    DatabaseManager, ConnectionPool, TransactionManager, DatabaseError, DatabaseConfig,","highlight_start":20,"highlight_end":56}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\infrastructure\\mod.rs","byte_start":1193,"byte_end":1231,"line_start":29,"line_end":30,"column_start":87,"column_end":36,"is_primary":true,"text":[{"text":"    DatabaseManager, ConnectionPool, TransactionManager, DatabaseError, DatabaseConfig,","highlight_start":87,"highlight_end":88},{"text":"    DatabaseStats, MigrationManager","highlight_start":1,"highlight_end":36}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `ConnectionPool`, `DatabaseStats`, `MigrationManager`, and `TransactionManager`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\mod.rs:29:22\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m29\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    DatabaseManager, ConnectionPool, TransactionManager, DatabaseError, DatabaseConfig,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m30\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    DatabaseStats, MigrationManager\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `InfrastructureError`, `InfrastructureHealth`, and `ServiceHealth`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\mod.rs","byte_start":1304,"byte_end":1323,"line_start":33,"line_end":33,"column_start":51,"column_end":70,"is_primary":true,"text":[{"text":"    InfrastructureServices, InfrastructureConfig, InfrastructureError,","highlight_start":51,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\mod.rs","byte_start":1330,"byte_end":1350,"line_start":34,"line_end":34,"column_start":5,"column_end":25,"is_primary":true,"text":[{"text":"    InfrastructureHealth, ServiceHealth","highlight_start":5,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\mod.rs","byte_start":1352,"byte_end":1365,"line_start":34,"line_end":34,"column_start":27,"column_end":40,"is_primary":true,"text":[{"text":"    InfrastructureHealth, ServiceHealth","highlight_start":27,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\mod.rs","byte_start":1302,"byte_end":1365,"line_start":33,"line_end":34,"column_start":49,"column_end":40,"is_primary":true,"text":[{"text":"    InfrastructureServices, InfrastructureConfig, InfrastructureError,","highlight_start":49,"highlight_end":71},{"text":"    InfrastructureHealth, ServiceHealth","highlight_start":1,"highlight_end":40}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `InfrastructureError`, `InfrastructureHealth`, and `ServiceHealth`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\mod.rs:33:51\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    InfrastructureServices, InfrastructureConfig, InfrastructureError,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m34\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    InfrastructureHealth, ServiceHealth\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"conflicting implementations of trait `std::fmt::Debug` for type `infrastructure::event_bus::EventStats`","code":{"code":"E0119","explanation":"There are conflicting trait implementations for the same type.\n\nErroneous code example:\n\n```compile_fail,E0119\ntrait MyTrait {\n    fn get(&self) -> usize;\n}\n\nimpl<T> MyTrait for T {\n    fn get(&self) -> usize { 0 }\n}\n\nstruct Foo {\n    value: usize\n}\n\nimpl MyTrait for Foo { // error: conflicting implementations of trait\n                       //        `MyTrait` for type `Foo`\n    fn get(&self) -> usize { self.value }\n}\n```\n\nWhen looking for the implementation for the trait, the compiler finds\nboth the `impl<T> MyTrait for T` where T is all types and the `impl\nMyTrait for Foo`. Since a trait cannot be implemented multiple times,\nthis is an error. So, when you write:\n\n```\ntrait MyTrait {\n    fn get(&self) -> usize;\n}\n\nimpl<T> MyTrait for T {\n    fn get(&self) -> usize { 0 }\n}\n```\n\nThis makes the trait implemented on all types in the scope. So if you\ntry to implement it on another one after that, the implementations will\nconflict. Example:\n\n```\ntrait MyTrait {\n    fn get(&self) -> usize;\n}\n\nimpl<T> MyTrait for T {\n    fn get(&self) -> usize { 0 }\n}\n\nstruct Foo;\n\nfn main() {\n    let f = Foo;\n\n    f.get(); // the trait is implemented so we can use it\n}\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\event_bus.rs","byte_start":3674,"byte_end":3679,"line_start":140,"line_end":140,"column_start":10,"column_end":15,"is_primary":false,"text":[{"text":"#[derive(Debug, Default)]","highlight_start":10,"highlight_end":15}],"label":"first implementation here","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\infrastructure\\event_bus.rs","byte_start":3674,"byte_end":3679,"line_start":140,"line_end":140,"column_start":10,"column_end":15,"is_primary":false,"text":[{"text":"#[derive(Debug, Default)]","highlight_start":10,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[derive(Debug)]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.rustup\\toolchains\\stable-x86_64-pc-windows-msvc\\lib/rustlib/src/rust\\library\\core\\src\\fmt\\mod.rs","byte_start":35462,"byte_end":35477,"line_start":942,"line_end":942,"column_start":5,"column_end":20,"is_primary":false,"text":[{"text":"    pub macro Debug($item:item) {","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\infrastructure\\event_bus.rs","byte_start":7811,"byte_end":7816,"line_start":256,"line_end":256,"column_start":10,"column_end":15,"is_primary":true,"text":[{"text":"#[derive(Debug, Clone)]","highlight_start":10,"highlight_end":15}],"label":"conflicting implementation for `infrastructure::event_bus::EventStats`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\infrastructure\\event_bus.rs","byte_start":7811,"byte_end":7816,"line_start":256,"line_end":256,"column_start":10,"column_end":15,"is_primary":false,"text":[{"text":"#[derive(Debug, Clone)]","highlight_start":10,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[derive(Debug)]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.rustup\\toolchains\\stable-x86_64-pc-windows-msvc\\lib/rustlib/src/rust\\library\\core\\src\\fmt\\mod.rs","byte_start":35462,"byte_end":35477,"line_start":942,"line_end":942,"column_start":5,"column_end":20,"is_primary":false,"text":[{"text":"    pub macro Debug($item:item) {","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0119]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: conflicting implementations of trait `std::fmt::Debug` for type `infrastructure::event_bus::EventStats`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\event_bus.rs:256:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m140\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[derive(Debug, Default)]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfirst implementation here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m256\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[derive(Debug, Clone)]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mconflicting implementation for `infrastructure::event_bus::EventStats`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `get_async_connection` found for struct `redis::Client` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":3116,"byte_end":3136,"line_start":100,"line_end":100,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `get_connection` with a similar name","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":3116,"byte_end":3136,"line_start":100,"line_end":100,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":"get_connection","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `get_async_connection` found for struct `redis::Client` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:100:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m100\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `get_connection` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m100\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;9mget_async_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m100\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;10mget_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `get_async_connection` found for struct `redis::Client` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":3517,"byte_end":3537,"line_start":113,"line_end":113,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `get_connection` with a similar name","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":3517,"byte_end":3537,"line_start":113,"line_end":113,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":"get_connection","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `get_async_connection` found for struct `redis::Client` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:113:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m113\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `get_connection` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m113\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;9mget_async_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m113\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;10mget_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `get_async_connection` found for struct `redis::Client` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":4145,"byte_end":4165,"line_start":132,"line_end":132,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `get_connection` with a similar name","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":4145,"byte_end":4165,"line_start":132,"line_end":132,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":"get_connection","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `get_async_connection` found for struct `redis::Client` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:132:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m132\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `get_connection` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m132\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;9mget_async_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m132\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;10mget_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `get_async_connection` found for struct `redis::Client` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":4489,"byte_end":4509,"line_start":141,"line_end":141,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `get_connection` with a similar name","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":4489,"byte_end":4509,"line_start":141,"line_end":141,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":"get_connection","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `get_async_connection` found for struct `redis::Client` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:141:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m141\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `get_connection` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m141\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;9mget_async_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m141\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;10mget_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `get_async_connection` found for struct `redis::Client` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":4767,"byte_end":4787,"line_start":149,"line_end":149,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `get_connection` with a similar name","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":4767,"byte_end":4787,"line_start":149,"line_end":149,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":"get_connection","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `get_async_connection` found for struct `redis::Client` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:149:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m149\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `get_connection` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m149\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;9mget_async_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m149\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;10mget_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `get_async_connection` found for struct `redis::Client` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":5118,"byte_end":5138,"line_start":160,"line_end":160,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `get_connection` with a similar name","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":5118,"byte_end":5138,"line_start":160,"line_end":160,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":"get_connection","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `get_async_connection` found for struct `redis::Client` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:160:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m160\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `get_connection` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m160\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;9mget_async_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m160\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;10mget_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `get_async_connection` found for struct `redis::Client` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":5792,"byte_end":5812,"line_start":182,"line_end":182,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `get_connection` with a similar name","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":5792,"byte_end":5812,"line_start":182,"line_end":182,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":"get_connection","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `get_async_connection` found for struct `redis::Client` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:182:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m182\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `get_connection` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m182\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;9mget_async_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m182\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;10mget_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `get_async_connection` found for struct `redis::Client` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":6192,"byte_end":6212,"line_start":194,"line_end":194,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `get_connection` with a similar name","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":6192,"byte_end":6212,"line_start":194,"line_end":194,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":"get_connection","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `get_async_connection` found for struct `redis::Client` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:194:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m194\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `get_connection` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m194\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;9mget_async_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m194\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;10mget_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `get_async_connection` found for struct `redis::Client` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":6572,"byte_end":6592,"line_start":206,"line_end":206,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `get_connection` with a similar name","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":6572,"byte_end":6592,"line_start":206,"line_end":206,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":"get_connection","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `get_async_connection` found for struct `redis::Client` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:206:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m206\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `get_connection` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m206\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;9mget_async_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m206\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;10mget_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `get_async_connection` found for struct `redis::Client` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":6959,"byte_end":6979,"line_start":222,"line_end":222,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `get_connection` with a similar name","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":6959,"byte_end":6979,"line_start":222,"line_end":222,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":"get_connection","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `get_async_connection` found for struct `redis::Client` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:222:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m222\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `get_connection` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m222\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;9mget_async_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m222\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;10mget_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `get_async_connection` found for struct `redis::Client` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":7516,"byte_end":7536,"line_start":240,"line_end":240,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `get_connection` with a similar name","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":7516,"byte_end":7536,"line_start":240,"line_end":240,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":"get_connection","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `get_async_connection` found for struct `redis::Client` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:240:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m240\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `get_connection` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m240\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;9mget_async_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m240\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;10mget_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `get_async_connection` found for struct `redis::Client` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":7841,"byte_end":7861,"line_start":251,"line_end":251,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `get_connection` with a similar name","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":7841,"byte_end":7861,"line_start":251,"line_end":251,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":"get_connection","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `get_async_connection` found for struct `redis::Client` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:251:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m251\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `get_connection` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m251\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;9mget_async_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m251\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;10mget_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `get_async_connection` found for struct `redis::Client` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":8291,"byte_end":8311,"line_start":265,"line_end":265,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `get_connection` with a similar name","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":8291,"byte_end":8311,"line_start":265,"line_end":265,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":"get_connection","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `get_async_connection` found for struct `redis::Client` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:265:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m265\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `get_connection` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m265\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;9mget_async_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m265\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;10mget_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `query_async` found for struct `redis::Cmd` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":8366,"byte_end":8377,"line_start":266,"line_end":266,"column_start":44,"column_end":55,"is_primary":true,"text":[{"text":"        let _: () = redis::cmd(\"FLUSHALL\").query_async(&mut conn).await?;","highlight_start":44,"highlight_end":55}],"label":"method not found in `redis::Cmd`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `query_async` found for struct `redis::Cmd` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:266:44\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m266\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let _: () = redis::cmd(\"FLUSHALL\").query_async(&mut conn).await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `redis::Cmd`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `get_async_connection` found for struct `redis::Client` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":8615,"byte_end":8635,"line_start":274,"line_end":274,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `get_connection` with a similar name","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":8615,"byte_end":8635,"line_start":274,"line_end":274,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":"get_connection","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `get_async_connection` found for struct `redis::Client` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:274:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m274\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `get_connection` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m274\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;9mget_async_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m274\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        let mut conn = self.client.\u001b[0m\u001b[0m\u001b[38;5;10mget_connection\u001b[0m\u001b[0m().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `query_async` found for mutable reference `&mut redis::Cmd` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":8674,"byte_end":8735,"line_start":275,"line_end":277,"column_start":28,"column_end":14,"is_primary":false,"text":[{"text":"        let info: String = redis::cmd(\"INFO\")","highlight_start":28,"highlight_end":46},{"text":"            .arg(\"memory\")","highlight_start":1,"highlight_end":27},{"text":"            .query_async(&mut conn)","highlight_start":1,"highlight_end":14}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\cache.rs","byte_start":8735,"byte_end":8746,"line_start":277,"line_end":277,"column_start":14,"column_end":25,"is_primary":true,"text":[{"text":"            .query_async(&mut conn)","highlight_start":14,"highlight_end":25}],"label":"method not found in `&mut Cmd`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `query_async` found for mutable reference `&mut redis::Cmd` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:277:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m275\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let info: String = redis::cmd(\"INFO\")\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m ____________________________-\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m276\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .arg(\"memory\")\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m277\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .query_async(&mut conn)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `&mut Cmd`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|_____________|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":10675,"byte_end":10681,"line_start":346,"line_end":346,"column_start":32,"column_end":38,"is_primary":true,"text":[{"text":"        let session_id = Uuid::new_v4().to_string();","highlight_start":32,"highlight_end":38}],"label":"function or associated item not found in `uuid::Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\nuuid::builder::<impl uuid::Uuid>::nil\nuuid::builder::<impl uuid::Uuid>::max\nuuid::builder::<impl uuid::Uuid>::from_fields\nuuid::builder::<impl uuid::Uuid>::from_fields_le\nand 12 others","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2247,"byte_end":2273,"line_start":72,"line_end":72,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn nil() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2872,"byte_end":2898,"line_start":98,"line_end":98,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn max() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":3453,"byte_end":3526,"line_start":122,"line_end":122,"column_start":5,"column_end":78,"is_primary":true,"text":[{"text":"    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":4752,"byte_end":4828,"line_start":168,"line_end":168,"column_start":5,"column_end":81,"is_primary":true,"text":[{"text":"    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `new_v7` with a similar name","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs","byte_start":2283,"byte_end":2319,"line_start":63,"line_end":63,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn new_v7(ts: Timestamp) -> Self {","highlight_start":5,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:346:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m346\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let session_id = Uuid::new_v4().to_string();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::nil\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::max\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields_le\u001b[0m\n\u001b[0m      and 12 others\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs:72:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn nil() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn max() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `new_v7` with a similar name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs:63:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_v7(ts: Timestamp) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\notifications.rs","byte_start":15028,"byte_end":15034,"line_start":454,"line_end":454,"column_start":23,"column_end":29,"is_primary":true,"text":[{"text":"            id: Uuid::new_v4(),","highlight_start":23,"highlight_end":29}],"label":"function or associated item not found in `uuid::Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\nuuid::builder::<impl uuid::Uuid>::nil\nuuid::builder::<impl uuid::Uuid>::max\nuuid::builder::<impl uuid::Uuid>::from_fields\nuuid::builder::<impl uuid::Uuid>::from_fields_le\nand 12 others","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2247,"byte_end":2273,"line_start":72,"line_end":72,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn nil() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2872,"byte_end":2898,"line_start":98,"line_end":98,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn max() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":3453,"byte_end":3526,"line_start":122,"line_end":122,"column_start":5,"column_end":78,"is_primary":true,"text":[{"text":"    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":4752,"byte_end":4828,"line_start":168,"line_end":168,"column_start":5,"column_end":81,"is_primary":true,"text":[{"text":"    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `new_v7` with a similar name","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs","byte_start":2283,"byte_end":2319,"line_start":63,"line_end":63,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn new_v7(ts: Timestamp) -> Self {","highlight_start":5,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\notifications.rs:454:23\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m454\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            id: Uuid::new_v4(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::nil\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::max\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields_le\u001b[0m\n\u001b[0m      and 12 others\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs:72:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn nil() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn max() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `new_v7` with a similar name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs:63:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_v7(ts: Timestamp) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\audit.rs","byte_start":14029,"byte_end":14035,"line_start":482,"line_end":482,"column_start":23,"column_end":29,"is_primary":true,"text":[{"text":"            id: Uuid::new_v4(),","highlight_start":23,"highlight_end":29}],"label":"function or associated item not found in `uuid::Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\nuuid::builder::<impl uuid::Uuid>::nil\nuuid::builder::<impl uuid::Uuid>::max\nuuid::builder::<impl uuid::Uuid>::from_fields\nuuid::builder::<impl uuid::Uuid>::from_fields_le\nand 12 others","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2247,"byte_end":2273,"line_start":72,"line_end":72,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn nil() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2872,"byte_end":2898,"line_start":98,"line_end":98,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn max() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":3453,"byte_end":3526,"line_start":122,"line_end":122,"column_start":5,"column_end":78,"is_primary":true,"text":[{"text":"    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":4752,"byte_end":4828,"line_start":168,"line_end":168,"column_start":5,"column_end":81,"is_primary":true,"text":[{"text":"    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `new_v7` with a similar name","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs","byte_start":2283,"byte_end":2319,"line_start":63,"line_end":63,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn new_v7(ts: Timestamp) -> Self {","highlight_start":5,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\audit.rs:482:23\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m482\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            id: Uuid::new_v4(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::nil\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::max\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields_le\u001b[0m\n\u001b[0m      and 12 others\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs:72:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn nil() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn max() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `new_v7` with a similar name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs:63:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_v7(ts: Timestamp) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this function takes 1 argument but 0 arguments were supplied","code":{"code":"E0057","explanation":"An invalid number of arguments was given when calling a closure.\n\nErroneous code example:\n\n```compile_fail,E0057\nlet f = |x| x * 3;\nlet a = f();        // invalid, too few parameters\nlet b = f(4);       // this works!\nlet c = f(2, 3);    // invalid, too many parameters\n```\n\nWhen invoking closures or other implementations of the function traits `Fn`,\n`FnMut` or `FnOnce` using call notation, the number of parameters passed to the\nfunction must match its definition.\n\nA generic function must be treated similarly:\n\n```\nfn foo<F: Fn()>(f: F) {\n    f(); // this is valid, but f(3) would not work\n}\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\database.rs","byte_start":9117,"byte_end":9119,"line_start":275,"line_end":275,"column_start":28,"column_end":30,"is_primary":false,"text":[{"text":"            match operation().await {","highlight_start":28,"highlight_end":30}],"label":"argument #1 is missing","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\database.rs","byte_start":9108,"byte_end":9117,"line_start":275,"line_end":275,"column_start":19,"column_end":28,"is_primary":true,"text":[{"text":"            match operation().await {","highlight_start":19,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"callable defined here","code":null,"level":"note","spans":[{"file_name":"src\\infrastructure\\database.rs","byte_start":8925,"byte_end":9023,"line_start":270,"line_end":270,"column_start":12,"column_end":110,"is_primary":true,"text":[{"text":"        F: Fn() -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<R, DatabaseError>> + Send + '_>>,","highlight_start":12,"highlight_end":110}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"provide the argument","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\database.rs","byte_start":9117,"byte_end":9119,"line_start":275,"line_end":275,"column_start":28,"column_end":30,"is_primary":true,"text":[{"text":"            match operation().await {","highlight_start":28,"highlight_end":30}],"label":null,"suggested_replacement":"(/* value */)","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0057]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: this function takes 1 argument but 0 arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\database.rs:275:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m275\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            match operation().await {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14margument #1 is missing\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: callable defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\database.rs:270:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m270\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        F: Fn() -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<R, DatabaseError>> + Send + '_>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: provide the argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m275\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m            match operation(\u001b[0m\u001b[0m\u001b[38;5;10m/* value */\u001b[0m\u001b[0m).await {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[38;5;10m+++++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"future cannot be sent between threads safely","code":null,"level":"error","spans":[{"file_name":"src\\infrastructure\\database.rs","byte_start":10316,"byte_end":10614,"line_start":301,"line_end":310,"column_start":13,"column_end":15,"is_primary":true,"text":[{"text":"            Box::pin(async move {","highlight_start":13,"highlight_end":34},{"text":"                let mut results = Vec::new();","highlight_start":1,"highlight_end":46},{"text":"                ","highlight_start":1,"highlight_end":17},{"text":"                for operation in operations {","highlight_start":1,"highlight_end":46},{"text":"                    operation(txn).await?;","highlight_start":1,"highlight_end":43},{"text":"                    results.push(());","highlight_start":1,"highlight_end":38},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"                ","highlight_start":1,"highlight_end":17},{"text":"                Ok(results)","highlight_start":1,"highlight_end":28},{"text":"            })","highlight_start":1,"highlight_end":15}],"label":"future created by async block is not `Send`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"captured value is not `Send`","code":null,"level":"note","spans":[{"file_name":"src\\infrastructure\\database.rs","byte_start":10437,"byte_end":10447,"line_start":304,"line_end":304,"column_start":34,"column_end":44,"is_primary":true,"text":[{"text":"                for operation in operations {","highlight_start":34,"highlight_end":44}],"label":"has type `std::vec::Vec<F>` which is not `Send`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"required for the cast from `std::pin::Pin<std::boxed::Box<{async block@src\\infrastructure\\database.rs:301:22: 301:32}>>` to `Pin<Box<dyn Future<Output = Result<Vec<()>, DatabaseError>> + Send>>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the full name for the type has been written to 'D:\\programming\\desktop-apps\\latest-meditrack\\meditrack\\src-tauri\\target\\debug\\deps\\app_lib-1ac6da61df638189.long-type-15834565054103017289.txt'","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider using `--verbose` to print the full type name to the console","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider further restricting type parameter `F` with trait `Send`","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\database.rs","byte_start":10252,"byte_end":10252,"line_start":298,"line_end":298,"column_start":146,"column_end":146,"is_primary":true,"text":[{"text":"        F: for<'a> FnOnce(&'a DatabaseTransaction) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<(), DatabaseError>> + Send + 'a>>,","highlight_start":146,"highlight_end":146}],"label":null,"suggested_replacement":" + std::marker::Send","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: future cannot be sent between threads safely\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\database.rs:301:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m301\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m            Box::pin(async move {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m302\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                let mut results = Vec::new();\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m303\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                \u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m304\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                for operation in operations {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m309\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                Ok(results)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m310\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            })\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|______________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfuture created by async block is not `Send`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: captured value is not `Send`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\database.rs:304:34\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m304\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                for operation in operations {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mhas type `std::vec::Vec<F>` which is not `Send`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: required for the cast from `std::pin::Pin<std::boxed::Box<{async block@src\\infrastructure\\database.rs:301:22: 301:32}>>` to `Pin<Box<dyn Future<Output = Result<Vec<()>, DatabaseError>> + Send>>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the full name for the type has been written to 'D:\\programming\\desktop-apps\\latest-meditrack\\meditrack\\src-tauri\\target\\debug\\deps\\app_lib-1ac6da61df638189.long-type-15834565054103017289.txt'\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `--verbose` to print the full type name to the console\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider further restricting type parameter `F` with trait `Send`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m298\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m        F: for<'a> FnOnce(&'a DatabaseTransaction) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<(), DatabaseError>> + Send + 'a>>\u001b[0m\u001b[0m\u001b[38;5;10m + std::marker::Send\u001b[0m\u001b[0m,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                                                                                   \u001b[0m\u001b[0m\u001b[38;5;10m+++++++++++++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `clone` found for struct `infrastructure::notifications::NotificationService` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\core.rs","byte_start":5410,"byte_end":5415,"line_start":144,"line_end":144,"column_start":85,"column_end":90,"is_primary":true,"text":[{"text":"        let notification_handler = NotificationEventHandler::new(self.notifications.clone());","highlight_start":85,"highlight_end":90}],"label":"method not found in `NotificationService`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\notifications.rs","byte_start":10549,"byte_end":10579,"line_start":351,"line_end":351,"column_start":1,"column_end":31,"is_primary":false,"text":[{"text":"pub struct NotificationService {","highlight_start":1,"highlight_end":31}],"label":"method `clone` not found for this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"items from traits can only be used if the trait is implemented and in scope","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the following trait defines an item `clone`, perhaps you need to implement it:\ncandidate #1: `std::clone::Clone`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `clone` found for struct `infrastructure::notifications::NotificationService` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\core.rs:144:85\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m144\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let notification_handler = NotificationEventHandler::new(self.notifications.clone());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `NotificationService`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0msrc\\infrastructure\\notifications.rs:351:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m351\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct NotificationService {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethod `clone` not found for this struct\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: items from traits can only be used if the trait is implemented and in scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the following trait defines an item `clone`, perhaps you need to implement it:\u001b[0m\n\u001b[0m            candidate #1: `std::clone::Clone`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `clone` found for struct `infrastructure::cache::CacheService` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\core.rs","byte_start":5819,"byte_end":5824,"line_start":155,"line_end":155,"column_start":63,"column_end":68,"is_primary":true,"text":[{"text":"        let cache_handler = CacheEventHandler::new(self.cache.clone());","highlight_start":63,"highlight_end":68}],"label":"method not found in `CacheService`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\cache.rs","byte_start":1720,"byte_end":1743,"line_start":53,"line_end":53,"column_start":1,"column_end":24,"is_primary":false,"text":[{"text":"pub struct CacheService {","highlight_start":1,"highlight_end":24}],"label":"method `clone` not found for this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"items from traits can only be used if the trait is implemented and in scope","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the following trait defines an item `clone`, perhaps you need to implement it:\ncandidate #1: `std::clone::Clone`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `clone` found for struct `infrastructure::cache::CacheService` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\core.rs:155:63\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m155\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let cache_handler = CacheEventHandler::new(self.cache.clone());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `CacheService`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:53:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m53\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct CacheService {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethod `clone` not found for this struct\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: items from traits can only be used if the trait is implemented and in scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the following trait defines an item `clone`, perhaps you need to implement it:\u001b[0m\n\u001b[0m            candidate #1: `std::clone::Clone`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `event_stats`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\core.rs","byte_start":10555,"byte_end":10566,"line_start":273,"line_end":273,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"        let event_stats = self.event_bus.get_stats().await;","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\core.rs","byte_start":10555,"byte_end":10566,"line_start":273,"line_end":273,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"        let event_stats = self.event_bus.get_stats().await;","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":"_event_stats","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `event_stats`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\core.rs:273:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m273\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let event_stats = self.event_bus.get_stats().await;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_event_stats`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `audit_stats`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\core.rs","byte_start":10887,"byte_end":10898,"line_start":282,"line_end":282,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"        let audit_stats = self.audit.get_stats().await;","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\core.rs","byte_start":10887,"byte_end":10898,"line_start":282,"line_end":282,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"        let audit_stats = self.audit.get_stats().await;","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":"_audit_stats","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `audit_stats`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\core.rs:282:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m282\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let audit_stats = self.audit.get_stats().await;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_audit_stats`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":767,"byte_end":773,"line_start":22,"line_end":22,"column_start":28,"column_end":34,"is_primary":true,"text":[{"text":"    let product_id = Uuid::new_v4();","highlight_start":28,"highlight_end":34}],"label":"function or associated item not found in `uuid::Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\nuuid::builder::<impl uuid::Uuid>::nil\nuuid::builder::<impl uuid::Uuid>::max\nuuid::builder::<impl uuid::Uuid>::from_fields\nuuid::builder::<impl uuid::Uuid>::from_fields_le\nand 12 others","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2247,"byte_end":2273,"line_start":72,"line_end":72,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn nil() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2872,"byte_end":2898,"line_start":98,"line_end":98,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn max() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":3453,"byte_end":3526,"line_start":122,"line_end":122,"column_start":5,"column_end":78,"is_primary":true,"text":[{"text":"    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":4752,"byte_end":4828,"line_start":168,"line_end":168,"column_start":5,"column_end":81,"is_primary":true,"text":[{"text":"    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `new_v7` with a similar name","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs","byte_start":2283,"byte_end":2319,"line_start":63,"line_end":63,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn new_v7(ts: Timestamp) -> Self {","highlight_start":5,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\examples.rs:22:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m22\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let product_id = Uuid::new_v4();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::nil\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::max\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields_le\u001b[0m\n\u001b[0m      and 12 others\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs:72:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn nil() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn max() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `new_v7` with a similar name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs:63:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_v7(ts: Timestamp) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":806,"byte_end":812,"line_start":23,"line_end":23,"column_start":29,"column_end":35,"is_primary":true,"text":[{"text":"    let location_id = Uuid::new_v4();","highlight_start":29,"highlight_end":35}],"label":"function or associated item not found in `uuid::Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\nuuid::builder::<impl uuid::Uuid>::nil\nuuid::builder::<impl uuid::Uuid>::max\nuuid::builder::<impl uuid::Uuid>::from_fields\nuuid::builder::<impl uuid::Uuid>::from_fields_le\nand 12 others","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2247,"byte_end":2273,"line_start":72,"line_end":72,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn nil() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2872,"byte_end":2898,"line_start":98,"line_end":98,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn max() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":3453,"byte_end":3526,"line_start":122,"line_end":122,"column_start":5,"column_end":78,"is_primary":true,"text":[{"text":"    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":4752,"byte_end":4828,"line_start":168,"line_end":168,"column_start":5,"column_end":81,"is_primary":true,"text":[{"text":"    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `new_v7` with a similar name","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs","byte_start":2283,"byte_end":2319,"line_start":63,"line_end":63,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn new_v7(ts: Timestamp) -> Self {","highlight_start":5,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\examples.rs:23:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m23\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let location_id = Uuid::new_v4();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::nil\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::max\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields_le\u001b[0m\n\u001b[0m      and 12 others\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs:72:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn nil() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn max() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `new_v7` with a similar name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs:63:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_v7(ts: Timestamp) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":841,"byte_end":847,"line_start":24,"line_end":24,"column_start":25,"column_end":31,"is_primary":true,"text":[{"text":"    let user_id = Uuid::new_v4();","highlight_start":25,"highlight_end":31}],"label":"function or associated item not found in `uuid::Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\nuuid::builder::<impl uuid::Uuid>::nil\nuuid::builder::<impl uuid::Uuid>::max\nuuid::builder::<impl uuid::Uuid>::from_fields\nuuid::builder::<impl uuid::Uuid>::from_fields_le\nand 12 others","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2247,"byte_end":2273,"line_start":72,"line_end":72,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn nil() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2872,"byte_end":2898,"line_start":98,"line_end":98,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn max() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":3453,"byte_end":3526,"line_start":122,"line_end":122,"column_start":5,"column_end":78,"is_primary":true,"text":[{"text":"    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":4752,"byte_end":4828,"line_start":168,"line_end":168,"column_start":5,"column_end":81,"is_primary":true,"text":[{"text":"    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `new_v7` with a similar name","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs","byte_start":2283,"byte_end":2319,"line_start":63,"line_end":63,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn new_v7(ts: Timestamp) -> Self {","highlight_start":5,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\examples.rs:24:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m24\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let user_id = Uuid::new_v4();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::nil\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::max\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields_le\u001b[0m\n\u001b[0m      and 12 others\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs:72:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn nil() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn max() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `new_v7` with a similar name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs:63:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_v7(ts: Timestamp) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":2532,"byte_end":2538,"line_start":70,"line_end":70,"column_start":23,"column_end":29,"is_primary":true,"text":[{"text":"            id: Uuid::new_v4(),","highlight_start":23,"highlight_end":29}],"label":"function or associated item not found in `uuid::Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\nuuid::builder::<impl uuid::Uuid>::nil\nuuid::builder::<impl uuid::Uuid>::max\nuuid::builder::<impl uuid::Uuid>::from_fields\nuuid::builder::<impl uuid::Uuid>::from_fields_le\nand 12 others","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2247,"byte_end":2273,"line_start":72,"line_end":72,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn nil() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2872,"byte_end":2898,"line_start":98,"line_end":98,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn max() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":3453,"byte_end":3526,"line_start":122,"line_end":122,"column_start":5,"column_end":78,"is_primary":true,"text":[{"text":"    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":4752,"byte_end":4828,"line_start":168,"line_end":168,"column_start":5,"column_end":81,"is_primary":true,"text":[{"text":"    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `new_v7` with a similar name","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs","byte_start":2283,"byte_end":2319,"line_start":63,"line_end":63,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn new_v7(ts: Timestamp) -> Self {","highlight_start":5,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\examples.rs:70:23\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m70\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            id: Uuid::new_v4(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::nil\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::max\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields_le\u001b[0m\n\u001b[0m      and 12 others\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs:72:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn nil() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn max() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `new_v7` with a similar name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs:63:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_v7(ts: Timestamp) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":3103,"byte_end":3109,"line_start":83,"line_end":83,"column_start":23,"column_end":29,"is_primary":true,"text":[{"text":"            id: Uuid::new_v4(),","highlight_start":23,"highlight_end":29}],"label":"function or associated item not found in `uuid::Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\nuuid::builder::<impl uuid::Uuid>::nil\nuuid::builder::<impl uuid::Uuid>::max\nuuid::builder::<impl uuid::Uuid>::from_fields\nuuid::builder::<impl uuid::Uuid>::from_fields_le\nand 12 others","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2247,"byte_end":2273,"line_start":72,"line_end":72,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn nil() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2872,"byte_end":2898,"line_start":98,"line_end":98,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn max() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":3453,"byte_end":3526,"line_start":122,"line_end":122,"column_start":5,"column_end":78,"is_primary":true,"text":[{"text":"    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":4752,"byte_end":4828,"line_start":168,"line_end":168,"column_start":5,"column_end":81,"is_primary":true,"text":[{"text":"    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `new_v7` with a similar name","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs","byte_start":2283,"byte_end":2319,"line_start":63,"line_end":63,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn new_v7(ts: Timestamp) -> Self {","highlight_start":5,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\examples.rs:83:23\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m83\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            id: Uuid::new_v4(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::nil\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::max\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields_le\u001b[0m\n\u001b[0m      and 12 others\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs:72:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn nil() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn max() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `new_v7` with a similar name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs:63:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_v7(ts: Timestamp) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":4281,"byte_end":4287,"line_start":113,"line_end":113,"column_start":33,"column_end":39,"is_primary":true,"text":[{"text":"    let prescription_id = Uuid::new_v4();","highlight_start":33,"highlight_end":39}],"label":"function or associated item not found in `uuid::Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\nuuid::builder::<impl uuid::Uuid>::nil\nuuid::builder::<impl uuid::Uuid>::max\nuuid::builder::<impl uuid::Uuid>::from_fields\nuuid::builder::<impl uuid::Uuid>::from_fields_le\nand 12 others","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2247,"byte_end":2273,"line_start":72,"line_end":72,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn nil() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2872,"byte_end":2898,"line_start":98,"line_end":98,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn max() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":3453,"byte_end":3526,"line_start":122,"line_end":122,"column_start":5,"column_end":78,"is_primary":true,"text":[{"text":"    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":4752,"byte_end":4828,"line_start":168,"line_end":168,"column_start":5,"column_end":81,"is_primary":true,"text":[{"text":"    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `new_v7` with a similar name","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs","byte_start":2283,"byte_end":2319,"line_start":63,"line_end":63,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn new_v7(ts: Timestamp) -> Self {","highlight_start":5,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\examples.rs:113:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m113\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let prescription_id = Uuid::new_v4();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::nil\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::max\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields_le\u001b[0m\n\u001b[0m      and 12 others\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs:72:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn nil() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn max() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `new_v7` with a similar name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs:63:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_v7(ts: Timestamp) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":4320,"byte_end":4326,"line_start":114,"line_end":114,"column_start":29,"column_end":35,"is_primary":true,"text":[{"text":"    let customer_id = Uuid::new_v4();","highlight_start":29,"highlight_end":35}],"label":"function or associated item not found in `uuid::Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\nuuid::builder::<impl uuid::Uuid>::nil\nuuid::builder::<impl uuid::Uuid>::max\nuuid::builder::<impl uuid::Uuid>::from_fields\nuuid::builder::<impl uuid::Uuid>::from_fields_le\nand 12 others","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2247,"byte_end":2273,"line_start":72,"line_end":72,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn nil() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2872,"byte_end":2898,"line_start":98,"line_end":98,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn max() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":3453,"byte_end":3526,"line_start":122,"line_end":122,"column_start":5,"column_end":78,"is_primary":true,"text":[{"text":"    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":4752,"byte_end":4828,"line_start":168,"line_end":168,"column_start":5,"column_end":81,"is_primary":true,"text":[{"text":"    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `new_v7` with a similar name","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs","byte_start":2283,"byte_end":2319,"line_start":63,"line_end":63,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn new_v7(ts: Timestamp) -> Self {","highlight_start":5,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\examples.rs:114:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m114\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let customer_id = Uuid::new_v4();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::nil\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::max\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields_le\u001b[0m\n\u001b[0m      and 12 others\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs:72:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn nil() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn max() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `new_v7` with a similar name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs:63:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_v7(ts: Timestamp) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":4361,"byte_end":4367,"line_start":115,"line_end":115,"column_start":31,"column_end":37,"is_primary":true,"text":[{"text":"    let pharmacist_id = Uuid::new_v4();","highlight_start":31,"highlight_end":37}],"label":"function or associated item not found in `uuid::Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\nuuid::builder::<impl uuid::Uuid>::nil\nuuid::builder::<impl uuid::Uuid>::max\nuuid::builder::<impl uuid::Uuid>::from_fields\nuuid::builder::<impl uuid::Uuid>::from_fields_le\nand 12 others","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2247,"byte_end":2273,"line_start":72,"line_end":72,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn nil() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2872,"byte_end":2898,"line_start":98,"line_end":98,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn max() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":3453,"byte_end":3526,"line_start":122,"line_end":122,"column_start":5,"column_end":78,"is_primary":true,"text":[{"text":"    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":4752,"byte_end":4828,"line_start":168,"line_end":168,"column_start":5,"column_end":81,"is_primary":true,"text":[{"text":"    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `new_v7` with a similar name","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs","byte_start":2283,"byte_end":2319,"line_start":63,"line_end":63,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn new_v7(ts: Timestamp) -> Self {","highlight_start":5,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\examples.rs:115:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m115\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let pharmacist_id = Uuid::new_v4();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::nil\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::max\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields_le\u001b[0m\n\u001b[0m      and 12 others\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs:72:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn nil() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn max() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `new_v7` with a similar name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs:63:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_v7(ts: Timestamp) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":6160,"byte_end":6166,"line_start":165,"line_end":165,"column_start":19,"column_end":25,"is_primary":true,"text":[{"text":"        id: Uuid::new_v4(),","highlight_start":19,"highlight_end":25}],"label":"function or associated item not found in `uuid::Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\nuuid::builder::<impl uuid::Uuid>::nil\nuuid::builder::<impl uuid::Uuid>::max\nuuid::builder::<impl uuid::Uuid>::from_fields\nuuid::builder::<impl uuid::Uuid>::from_fields_le\nand 12 others","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2247,"byte_end":2273,"line_start":72,"line_end":72,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn nil() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2872,"byte_end":2898,"line_start":98,"line_end":98,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn max() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":3453,"byte_end":3526,"line_start":122,"line_end":122,"column_start":5,"column_end":78,"is_primary":true,"text":[{"text":"    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":4752,"byte_end":4828,"line_start":168,"line_end":168,"column_start":5,"column_end":81,"is_primary":true,"text":[{"text":"    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `new_v7` with a similar name","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs","byte_start":2283,"byte_end":2319,"line_start":63,"line_end":63,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn new_v7(ts: Timestamp) -> Self {","highlight_start":5,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\examples.rs:165:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m165\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        id: Uuid::new_v4(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::nil\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::max\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields_le\u001b[0m\n\u001b[0m      and 12 others\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs:72:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn nil() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn max() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `new_v7` with a similar name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs:63:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_v7(ts: Timestamp) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":8552,"byte_end":8558,"line_start":231,"line_end":231,"column_start":32,"column_end":38,"is_primary":true,"text":[{"text":"    let transaction_id = Uuid::new_v4();","highlight_start":32,"highlight_end":38}],"label":"function or associated item not found in `uuid::Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\nuuid::builder::<impl uuid::Uuid>::nil\nuuid::builder::<impl uuid::Uuid>::max\nuuid::builder::<impl uuid::Uuid>::from_fields\nuuid::builder::<impl uuid::Uuid>::from_fields_le\nand 12 others","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2247,"byte_end":2273,"line_start":72,"line_end":72,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn nil() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2872,"byte_end":2898,"line_start":98,"line_end":98,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn max() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":3453,"byte_end":3526,"line_start":122,"line_end":122,"column_start":5,"column_end":78,"is_primary":true,"text":[{"text":"    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":4752,"byte_end":4828,"line_start":168,"line_end":168,"column_start":5,"column_end":81,"is_primary":true,"text":[{"text":"    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `new_v7` with a similar name","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs","byte_start":2283,"byte_end":2319,"line_start":63,"line_end":63,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn new_v7(ts: Timestamp) -> Self {","highlight_start":5,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\examples.rs:231:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m231\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let transaction_id = Uuid::new_v4();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::nil\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::max\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields_le\u001b[0m\n\u001b[0m      and 12 others\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs:72:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn nil() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn max() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `new_v7` with a similar name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs:63:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_v7(ts: Timestamp) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":8590,"byte_end":8596,"line_start":232,"line_end":232,"column_start":28,"column_end":34,"is_primary":true,"text":[{"text":"    let account_id = Uuid::new_v4();","highlight_start":28,"highlight_end":34}],"label":"function or associated item not found in `uuid::Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\nuuid::builder::<impl uuid::Uuid>::nil\nuuid::builder::<impl uuid::Uuid>::max\nuuid::builder::<impl uuid::Uuid>::from_fields\nuuid::builder::<impl uuid::Uuid>::from_fields_le\nand 12 others","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2247,"byte_end":2273,"line_start":72,"line_end":72,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn nil() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2872,"byte_end":2898,"line_start":98,"line_end":98,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn max() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":3453,"byte_end":3526,"line_start":122,"line_end":122,"column_start":5,"column_end":78,"is_primary":true,"text":[{"text":"    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":4752,"byte_end":4828,"line_start":168,"line_end":168,"column_start":5,"column_end":81,"is_primary":true,"text":[{"text":"    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `new_v7` with a similar name","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs","byte_start":2283,"byte_end":2319,"line_start":63,"line_end":63,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn new_v7(ts: Timestamp) -> Self {","highlight_start":5,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\examples.rs:232:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m232\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let account_id = Uuid::new_v4();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::nil\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::max\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields_le\u001b[0m\n\u001b[0m      and 12 others\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs:72:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn nil() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn max() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `new_v7` with a similar name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs:63:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_v7(ts: Timestamp) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `events_published` of struct `infrastructure::event_bus::EventStats` is private","code":{"code":"E0616","explanation":"Attempted to access a private field on a struct.\n\nErroneous code example:\n\n```compile_fail,E0616\nmod some_module {\n    pub struct Foo {\n        x: u32, // So `x` is private in here.\n    }\n\n    impl Foo {\n        pub fn new() -> Foo { Foo { x: 0 } }\n    }\n}\n\nlet f = some_module::Foo::new();\nprintln!(\"{}\", f.x); // error: field `x` of struct `some_module::Foo` is private\n```\n\nIf you want to access this field, you have two options:\n\n1) Set the field public:\n\n```\nmod some_module {\n    pub struct Foo {\n        pub x: u32, // `x` is now public.\n    }\n\n    impl Foo {\n        pub fn new() -> Foo { Foo { x: 0 } }\n    }\n}\n\nlet f = some_module::Foo::new();\nprintln!(\"{}\", f.x); // ok!\n```\n\n2) Add a getter function:\n\n```\nmod some_module {\n    pub struct Foo {\n        x: u32, // So `x` is still private in here.\n    }\n\n    impl Foo {\n        pub fn new() -> Foo { Foo { x: 0 } }\n\n        // We create the getter function here:\n        pub fn get_x(&self) -> &u32 { &self.x }\n    }\n}\n\nlet f = some_module::Foo::new();\nprintln!(\"{}\", f.get_x()); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":10734,"byte_end":10750,"line_start":288,"line_end":288,"column_start":50,"column_end":66,"is_primary":true,"text":[{"text":"    println!(\"Events Published: {}\", event_stats.events_published);","highlight_start":50,"highlight_end":66}],"label":"private field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0616]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: field `events_published` of struct `infrastructure::event_bus::EventStats` is private\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\examples.rs:288:50\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m288\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    println!(\"Events Published: {}\", event_stats.events_published);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mprivate field\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `events_processed` of struct `infrastructure::event_bus::EventStats` is private","code":{"code":"E0616","explanation":"Attempted to access a private field on a struct.\n\nErroneous code example:\n\n```compile_fail,E0616\nmod some_module {\n    pub struct Foo {\n        x: u32, // So `x` is private in here.\n    }\n\n    impl Foo {\n        pub fn new() -> Foo { Foo { x: 0 } }\n    }\n}\n\nlet f = some_module::Foo::new();\nprintln!(\"{}\", f.x); // error: field `x` of struct `some_module::Foo` is private\n```\n\nIf you want to access this field, you have two options:\n\n1) Set the field public:\n\n```\nmod some_module {\n    pub struct Foo {\n        pub x: u32, // `x` is now public.\n    }\n\n    impl Foo {\n        pub fn new() -> Foo { Foo { x: 0 } }\n    }\n}\n\nlet f = some_module::Foo::new();\nprintln!(\"{}\", f.x); // ok!\n```\n\n2) Add a getter function:\n\n```\nmod some_module {\n    pub struct Foo {\n        x: u32, // So `x` is still private in here.\n    }\n\n    impl Foo {\n        pub fn new() -> Foo { Foo { x: 0 } }\n\n        // We create the getter function here:\n        pub fn get_x(&self) -> &u32 { &self.x }\n    }\n}\n\nlet f = some_module::Foo::new();\nprintln!(\"{}\", f.get_x()); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":10803,"byte_end":10819,"line_start":289,"line_end":289,"column_start":50,"column_end":66,"is_primary":true,"text":[{"text":"    println!(\"Events Processed: {}\", event_stats.events_processed);","highlight_start":50,"highlight_end":66}],"label":"private field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0616]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: field `events_processed` of struct `infrastructure::event_bus::EventStats` is private\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\examples.rs:289:50\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m289\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    println!(\"Events Processed: {}\", event_stats.events_processed);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mprivate field\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\event_bus.rs","byte_start":9157,"byte_end":9163,"line_start":304,"line_end":304,"column_start":31,"column_end":37,"is_primary":true,"text":[{"text":"            product_id: Uuid::new_v4(),","highlight_start":31,"highlight_end":37}],"label":"function or associated item not found in `uuid::Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\nuuid::builder::<impl uuid::Uuid>::nil\nuuid::builder::<impl uuid::Uuid>::max\nuuid::builder::<impl uuid::Uuid>::from_fields\nuuid::builder::<impl uuid::Uuid>::from_fields_le\nand 12 others","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2247,"byte_end":2273,"line_start":72,"line_end":72,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn nil() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2872,"byte_end":2898,"line_start":98,"line_end":98,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn max() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":3453,"byte_end":3526,"line_start":122,"line_end":122,"column_start":5,"column_end":78,"is_primary":true,"text":[{"text":"    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":4752,"byte_end":4828,"line_start":168,"line_end":168,"column_start":5,"column_end":81,"is_primary":true,"text":[{"text":"    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `new_v7` with a similar name","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs","byte_start":2283,"byte_end":2319,"line_start":63,"line_end":63,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn new_v7(ts: Timestamp) -> Self {","highlight_start":5,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\event_bus.rs:304:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m304\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            product_id: Uuid::new_v4(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::nil\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::max\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields_le\u001b[0m\n\u001b[0m      and 12 others\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs:72:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn nil() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn max() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `new_v7` with a similar name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs:63:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_v7(ts: Timestamp) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\event_bus.rs","byte_start":9199,"byte_end":9205,"line_start":305,"line_end":305,"column_start":32,"column_end":38,"is_primary":true,"text":[{"text":"            location_id: Uuid::new_v4(),","highlight_start":32,"highlight_end":38}],"label":"function or associated item not found in `uuid::Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\nuuid::builder::<impl uuid::Uuid>::nil\nuuid::builder::<impl uuid::Uuid>::max\nuuid::builder::<impl uuid::Uuid>::from_fields\nuuid::builder::<impl uuid::Uuid>::from_fields_le\nand 12 others","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2247,"byte_end":2273,"line_start":72,"line_end":72,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn nil() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2872,"byte_end":2898,"line_start":98,"line_end":98,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn max() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":3453,"byte_end":3526,"line_start":122,"line_end":122,"column_start":5,"column_end":78,"is_primary":true,"text":[{"text":"    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":4752,"byte_end":4828,"line_start":168,"line_end":168,"column_start":5,"column_end":81,"is_primary":true,"text":[{"text":"    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `new_v7` with a similar name","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs","byte_start":2283,"byte_end":2319,"line_start":63,"line_end":63,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn new_v7(ts: Timestamp) -> Self {","highlight_start":5,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\event_bus.rs:305:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m305\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            location_id: Uuid::new_v4(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::nil\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::max\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields_le\u001b[0m\n\u001b[0m      and 12 others\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs:72:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn nil() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn max() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `new_v7` with a similar name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs:63:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_v7(ts: Timestamp) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `clone` found for struct `infrastructure::event_bus::tests::TestHandler` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\event_bus.rs","byte_start":10186,"byte_end":10191,"line_start":333,"line_end":333,"column_start":44,"column_end":49,"is_primary":true,"text":[{"text":"        event_bus.register_handler(handler.clone(), vec![\"stock_updated\".to_string()]).await;","highlight_start":44,"highlight_end":49}],"label":"method not found in `TestHandler`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\event_bus.rs","byte_start":8048,"byte_end":8066,"line_start":268,"line_end":268,"column_start":5,"column_end":23,"is_primary":false,"text":[{"text":"    struct TestHandler {","highlight_start":5,"highlight_end":23}],"label":"method `clone` not found for this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"items from traits can only be used if the trait is implemented and in scope","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the following trait defines an item `clone`, perhaps you need to implement it:\ncandidate #1: `std::clone::Clone`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `clone` found for struct `infrastructure::event_bus::tests::TestHandler` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\event_bus.rs:333:44\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m268\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    struct TestHandler {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethod `clone` not found for this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m333\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        event_bus.register_handler(handler.clone(), vec![\"stock_updated\".to_string()]).await;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `TestHandler`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: items from traits can only be used if the trait is implemented and in scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the following trait defines an item `clone`, perhaps you need to implement it:\u001b[0m\n\u001b[0m            candidate #1: `std::clone::Clone`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\event_bus.rs","byte_start":10334,"byte_end":10340,"line_start":336,"line_end":336,"column_start":31,"column_end":37,"is_primary":true,"text":[{"text":"            product_id: Uuid::new_v4(),","highlight_start":31,"highlight_end":37}],"label":"function or associated item not found in `uuid::Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\nuuid::builder::<impl uuid::Uuid>::nil\nuuid::builder::<impl uuid::Uuid>::max\nuuid::builder::<impl uuid::Uuid>::from_fields\nuuid::builder::<impl uuid::Uuid>::from_fields_le\nand 12 others","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2247,"byte_end":2273,"line_start":72,"line_end":72,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn nil() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2872,"byte_end":2898,"line_start":98,"line_end":98,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn max() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":3453,"byte_end":3526,"line_start":122,"line_end":122,"column_start":5,"column_end":78,"is_primary":true,"text":[{"text":"    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":4752,"byte_end":4828,"line_start":168,"line_end":168,"column_start":5,"column_end":81,"is_primary":true,"text":[{"text":"    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `new_v7` with a similar name","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs","byte_start":2283,"byte_end":2319,"line_start":63,"line_end":63,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn new_v7(ts: Timestamp) -> Self {","highlight_start":5,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\event_bus.rs:336:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m336\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            product_id: Uuid::new_v4(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::nil\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::max\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields_le\u001b[0m\n\u001b[0m      and 12 others\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs:72:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn nil() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn max() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `new_v7` with a similar name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs:63:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_v7(ts: Timestamp) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\event_bus.rs","byte_start":10376,"byte_end":10382,"line_start":337,"line_end":337,"column_start":32,"column_end":38,"is_primary":true,"text":[{"text":"            location_id: Uuid::new_v4(),","highlight_start":32,"highlight_end":38}],"label":"function or associated item not found in `uuid::Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\nuuid::builder::<impl uuid::Uuid>::nil\nuuid::builder::<impl uuid::Uuid>::max\nuuid::builder::<impl uuid::Uuid>::from_fields\nuuid::builder::<impl uuid::Uuid>::from_fields_le\nand 12 others","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2247,"byte_end":2273,"line_start":72,"line_end":72,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn nil() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2872,"byte_end":2898,"line_start":98,"line_end":98,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn max() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":3453,"byte_end":3526,"line_start":122,"line_end":122,"column_start":5,"column_end":78,"is_primary":true,"text":[{"text":"    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":4752,"byte_end":4828,"line_start":168,"line_end":168,"column_start":5,"column_end":81,"is_primary":true,"text":[{"text":"    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `new_v7` with a similar name","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs","byte_start":2283,"byte_end":2319,"line_start":63,"line_end":63,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn new_v7(ts: Timestamp) -> Self {","highlight_start":5,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\event_bus.rs:337:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m337\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            location_id: Uuid::new_v4(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::nil\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::max\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields_le\u001b[0m\n\u001b[0m      and 12 others\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs:72:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn nil() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn max() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `new_v7` with a similar name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs:63:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_v7(ts: Timestamp) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":14623,"byte_end":14629,"line_start":471,"line_end":471,"column_start":29,"column_end":35,"is_primary":true,"text":[{"text":"        let user_id = Uuid::new_v4();","highlight_start":29,"highlight_end":35}],"label":"function or associated item not found in `uuid::Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\nuuid::builder::<impl uuid::Uuid>::nil\nuuid::builder::<impl uuid::Uuid>::max\nuuid::builder::<impl uuid::Uuid>::from_fields\nuuid::builder::<impl uuid::Uuid>::from_fields_le\nand 12 others","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2247,"byte_end":2273,"line_start":72,"line_end":72,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn nil() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2872,"byte_end":2898,"line_start":98,"line_end":98,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn max() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":3453,"byte_end":3526,"line_start":122,"line_end":122,"column_start":5,"column_end":78,"is_primary":true,"text":[{"text":"    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":4752,"byte_end":4828,"line_start":168,"line_end":168,"column_start":5,"column_end":81,"is_primary":true,"text":[{"text":"    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `new_v7` with a similar name","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs","byte_start":2283,"byte_end":2319,"line_start":63,"line_end":63,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn new_v7(ts: Timestamp) -> Self {","highlight_start":5,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:471:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m471\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let user_id = Uuid::new_v4();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::nil\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::max\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields_le\u001b[0m\n\u001b[0m      and 12 others\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs:72:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn nil() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn max() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `new_v7` with a similar name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs:63:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_v7(ts: Timestamp) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":14830,"byte_end":14836,"line_start":475,"line_end":475,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"            location_id: Some(Uuid::new_v4()),","highlight_start":37,"highlight_end":43}],"label":"function or associated item not found in `uuid::Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\nuuid::builder::<impl uuid::Uuid>::nil\nuuid::builder::<impl uuid::Uuid>::max\nuuid::builder::<impl uuid::Uuid>::from_fields\nuuid::builder::<impl uuid::Uuid>::from_fields_le\nand 12 others","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2247,"byte_end":2273,"line_start":72,"line_end":72,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn nil() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2872,"byte_end":2898,"line_start":98,"line_end":98,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn max() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":3453,"byte_end":3526,"line_start":122,"line_end":122,"column_start":5,"column_end":78,"is_primary":true,"text":[{"text":"    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":4752,"byte_end":4828,"line_start":168,"line_end":168,"column_start":5,"column_end":81,"is_primary":true,"text":[{"text":"    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `new_v7` with a similar name","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs","byte_start":2283,"byte_end":2319,"line_start":63,"line_end":63,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn new_v7(ts: Timestamp) -> Self {","highlight_start":5,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:475:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m475\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            location_id: Some(Uuid::new_v4()),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::nil\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::max\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields_le\u001b[0m\n\u001b[0m      and 12 others\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs:72:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn nil() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn max() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `new_v7` with a similar name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs:63:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_v7(ts: Timestamp) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type annotations needed","code":{"code":"E0282","explanation":"The compiler could not infer a type and asked for a type annotation.\n\nErroneous code example:\n\n```compile_fail,E0282\nlet x = Vec::new();\n```\n\nThis error indicates that type inference did not result in one unique possible\ntype, and extra information is required. In most cases this can be provided\nby adding a type annotation. Sometimes you need to specify a generic type\nparameter manually.\n\nIn the example above, type `Vec` has a type parameter `T`. When calling\n`Vec::new`, barring any other later usage of the variable `x` that allows the\ncompiler to infer what type `T` is, the compiler needs to be told what it is.\n\nThe type can be specified on the variable:\n\n```\nlet x: Vec<i32> = Vec::new();\n```\n\nThe type can also be specified in the path of the expression:\n\n```\nlet x = Vec::<i32>::new();\n```\n\nIn cases with more complex types, it is not necessary to annotate the full\ntype. Once the ambiguity is resolved, the compiler can infer the rest:\n\n```\nlet x: Vec<_> = \"hello\".chars().rev().collect();\n```\n\nAnother way to provide the compiler with enough information, is to specify the\ngeneric type parameter:\n\n```\nlet x = \"hello\".chars().rev().collect::<Vec<char>>();\n```\n\nAgain, you need not specify the full type if the compiler can infer it:\n\n```\nlet x = \"hello\".chars().rev().collect::<Vec<_>>();\n```\n\nApart from a method or function with a generic type parameter, this error can\noccur when a type parameter of a struct or trait cannot be inferred. In that\ncase it is not always possible to use a type annotation, because all candidates\nhave the same return type. For instance:\n\n```compile_fail,E0282\nstruct Foo<T> {\n    num: T,\n}\n\nimpl<T> Foo<T> {\n    fn bar() -> i32 {\n        0\n    }\n\n    fn baz() {\n        let number = Foo::bar();\n    }\n}\n```\n\nThis will fail because the compiler does not know which instance of `Foo` to\ncall `bar` on. Change `Foo::bar()` to `Foo::<T>::bar()` to resolve the error.\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\notifications.rs","byte_start":6500,"byte_end":6501,"line_start":221,"line_end":221,"column_start":64,"column_end":65,"is_primary":false,"text":[{"text":"            .map_err(|e| NotificationError::ConfigurationError(e.to_string()))?;","highlight_start":64,"highlight_end":65}],"label":"type must be known at this point","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\notifications.rs","byte_start":6459,"byte_end":6460,"line_start":221,"line_end":221,"column_start":23,"column_end":24,"is_primary":true,"text":[{"text":"            .map_err(|e| NotificationError::ConfigurationError(e.to_string()))?;","highlight_start":23,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider giving this closure parameter an explicit type","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\notifications.rs","byte_start":6460,"byte_end":6460,"line_start":221,"line_end":221,"column_start":24,"column_end":24,"is_primary":true,"text":[{"text":"            .map_err(|e| NotificationError::ConfigurationError(e.to_string()))?;","highlight_start":24,"highlight_end":24}],"label":null,"suggested_replacement":": /* Type */","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0282]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: type annotations needed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\notifications.rs:221:23\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m221\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .map_err(|e| NotificationError::ConfigurationError(e.to_string()))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mtype must be known at this point\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider giving this closure parameter an explicit type\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m221\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m            .map_err(|e\u001b[0m\u001b[0m\u001b[38;5;10m: /* Type */\u001b[0m\u001b[0m| NotificationError::ConfigurationError(e.to_string()))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[38;5;10m++++++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\notifications.rs","byte_start":19534,"byte_end":19540,"line_start":588,"line_end":588,"column_start":23,"column_end":29,"is_primary":true,"text":[{"text":"            id: Uuid::new_v4(),","highlight_start":23,"highlight_end":29}],"label":"function or associated item not found in `uuid::Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\nuuid::builder::<impl uuid::Uuid>::nil\nuuid::builder::<impl uuid::Uuid>::max\nuuid::builder::<impl uuid::Uuid>::from_fields\nuuid::builder::<impl uuid::Uuid>::from_fields_le\nand 12 others","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2247,"byte_end":2273,"line_start":72,"line_end":72,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn nil() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2872,"byte_end":2898,"line_start":98,"line_end":98,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn max() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":3453,"byte_end":3526,"line_start":122,"line_end":122,"column_start":5,"column_end":78,"is_primary":true,"text":[{"text":"    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":4752,"byte_end":4828,"line_start":168,"line_end":168,"column_start":5,"column_end":81,"is_primary":true,"text":[{"text":"    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `new_v7` with a similar name","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs","byte_start":2283,"byte_end":2319,"line_start":63,"line_end":63,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn new_v7(ts: Timestamp) -> Self {","highlight_start":5,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\notifications.rs:588:23\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m588\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            id: Uuid::new_v4(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::nil\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::max\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields_le\u001b[0m\n\u001b[0m      and 12 others\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs:72:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn nil() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn max() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `new_v7` with a similar name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs:63:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_v7(ts: Timestamp) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\audit.rs","byte_start":3370,"byte_end":3376,"line_start":129,"line_end":129,"column_start":23,"column_end":29,"is_primary":true,"text":[{"text":"            id: Uuid::new_v4(),","highlight_start":23,"highlight_end":29}],"label":"function or associated item not found in `uuid::Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\nuuid::builder::<impl uuid::Uuid>::nil\nuuid::builder::<impl uuid::Uuid>::max\nuuid::builder::<impl uuid::Uuid>::from_fields\nuuid::builder::<impl uuid::Uuid>::from_fields_le\nand 12 others","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2247,"byte_end":2273,"line_start":72,"line_end":72,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn nil() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":2872,"byte_end":2898,"line_start":98,"line_end":98,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    pub const fn max() -> Self {","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":3453,"byte_end":3526,"line_start":122,"line_end":122,"column_start":5,"column_end":78,"is_primary":true,"text":[{"text":"    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs","byte_start":4752,"byte_end":4828,"line_start":168,"line_end":168,"column_start":5,"column_end":81,"is_primary":true,"text":[{"text":"    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {","highlight_start":5,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"there is an associated function `new_v7` with a similar name","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs","byte_start":2283,"byte_end":2319,"line_start":63,"line_end":63,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    pub fn new_v7(ts: Timestamp) -> Self {","highlight_start":5,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no function or associated item named `new_v4` found for struct `uuid::Uuid` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\audit.rs:129:23\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m129\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            id: Uuid::new_v4(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: if you're trying to build a new `uuid::Uuid` consider using one of the following associated functions:\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::nil\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::max\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields\u001b[0m\n\u001b[0m      uuid::builder::<impl uuid::Uuid>::from_fields_le\u001b[0m\n\u001b[0m      and 12 others\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\builder.rs:72:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn nil() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn max() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn from_fields_le(d1: u32, d2: u16, d3: u16, d4: &[u8; 8]) -> Uuid {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an associated function `new_v7` with a similar name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\uuid-1.17.0\\src\\v7.rs:63:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_v7(ts: Timestamp) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `clone` found for enum `sea_orm::DatabaseConnection` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\audit.rs","byte_start":16210,"byte_end":16215,"line_start":548,"line_end":548,"column_start":25,"column_end":30,"is_primary":true,"text":[{"text":"            db: self.db.clone(),","highlight_start":25,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `close` with a similar name","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\audit.rs","byte_start":16210,"byte_end":16215,"line_start":548,"line_end":548,"column_start":25,"column_end":30,"is_primary":true,"text":[{"text":"            db: self.db.clone(),","highlight_start":25,"highlight_end":30}],"label":null,"suggested_replacement":"close","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `clone` found for enum `sea_orm::DatabaseConnection` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\audit.rs:548:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m548\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            db: self.db.clone(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `close` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m548\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            db: self.db.\u001b[0m\u001b[0m\u001b[38;5;9mclone\u001b[0m\u001b[0m(),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m548\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            db: self.db.\u001b[0m\u001b[0m\u001b[38;5;10mclose\u001b[0m\u001b[0m(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `clone` found for enum `sea_orm::DatabaseConnection` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\database.rs","byte_start":7114,"byte_end":7119,"line_start":216,"line_end":216,"column_start":41,"column_end":46,"is_primary":true,"text":[{"text":"            connection: self.connection.clone(),","highlight_start":41,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `close` with a similar name","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\database.rs","byte_start":7114,"byte_end":7119,"line_start":216,"line_end":216,"column_start":41,"column_end":46,"is_primary":true,"text":[{"text":"            connection: self.connection.clone(),","highlight_start":41,"highlight_end":46}],"label":null,"suggested_replacement":"close","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `clone` found for enum `sea_orm::DatabaseConnection` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\database.rs:216:41\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m216\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            connection: self.connection.clone(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `close` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m216\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            connection: self.connection.\u001b[0m\u001b[0m\u001b[38;5;9mclone\u001b[0m\u001b[0m(),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m216\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            connection: self.connection.\u001b[0m\u001b[0m\u001b[38;5;10mclose\u001b[0m\u001b[0m(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `clone` found for struct `infrastructure::cache::CacheService` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\core.rs","byte_start":12381,"byte_end":12386,"line_start":325,"line_end":325,"column_start":31,"column_end":36,"is_primary":true,"text":[{"text":"            cache: self.cache.clone(),","highlight_start":31,"highlight_end":36}],"label":"method not found in `CacheService`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\cache.rs","byte_start":1720,"byte_end":1743,"line_start":53,"line_end":53,"column_start":1,"column_end":24,"is_primary":false,"text":[{"text":"pub struct CacheService {","highlight_start":1,"highlight_end":24}],"label":"method `clone` not found for this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"items from traits can only be used if the trait is implemented and in scope","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the following trait defines an item `clone`, perhaps you need to implement it:\ncandidate #1: `std::clone::Clone`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `clone` found for struct `infrastructure::cache::CacheService` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\core.rs:325:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m325\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            cache: self.cache.clone(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `CacheService`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:53:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m53\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct CacheService {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethod `clone` not found for this struct\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: items from traits can only be used if the trait is implemented and in scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the following trait defines an item `clone`, perhaps you need to implement it:\u001b[0m\n\u001b[0m            candidate #1: `std::clone::Clone`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `clone` found for struct `infrastructure::notifications::NotificationService` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\core.rs","byte_start":12485,"byte_end":12490,"line_start":327,"line_end":327,"column_start":47,"column_end":52,"is_primary":true,"text":[{"text":"            notifications: self.notifications.clone(),","highlight_start":47,"highlight_end":52}],"label":"method not found in `NotificationService`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\notifications.rs","byte_start":10549,"byte_end":10579,"line_start":351,"line_end":351,"column_start":1,"column_end":31,"is_primary":false,"text":[{"text":"pub struct NotificationService {","highlight_start":1,"highlight_end":31}],"label":"method `clone` not found for this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"items from traits can only be used if the trait is implemented and in scope","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the following trait defines an item `clone`, perhaps you need to implement it:\ncandidate #1: `std::clone::Clone`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `clone` found for struct `infrastructure::notifications::NotificationService` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\core.rs:327:47\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m327\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            notifications: self.notifications.clone(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `NotificationService`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0msrc\\infrastructure\\notifications.rs:351:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m351\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct NotificationService {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethod `clone` not found for this struct\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: items from traits can only be used if the trait is implemented and in scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the following trait defines an item `clone`, perhaps you need to implement it:\u001b[0m\n\u001b[0m            candidate #1: `std::clone::Clone`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `ActiveModelTrait`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\audit.rs","byte_start":318,"byte_end":334,"line_start":7,"line_end":7,"column_start":15,"column_end":31,"is_primary":true,"text":[{"text":"use sea_orm::{ActiveModelTrait, Database, DatabaseConnection, EntityTrait, Set};","highlight_start":15,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `ActiveModelTrait`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\audit.rs:7:15\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse sea_orm::{ActiveModelTrait, Database, DatabaseConnection, EntityTrait, Set};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `EntityTrait`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\audit.rs","byte_start":366,"byte_end":377,"line_start":7,"line_end":7,"column_start":63,"column_end":74,"is_primary":true,"text":[{"text":"use sea_orm::{ActiveModelTrait, Database, DatabaseConnection, EntityTrait, Set};","highlight_start":63,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `EntityTrait`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\audit.rs:7:63\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse sea_orm::{ActiveModelTrait, Database, DatabaseConnection, EntityTrait, Set};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `severity`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\core.rs","byte_start":14637,"byte_end":14645,"line_start":390,"line_end":390,"column_start":105,"column_end":113,"is_primary":true,"text":[{"text":"            super::PharmacyEvent::LowStockAlert { product_id, location_id, current_quantity, threshold, severity, .. } => {","highlight_start":105,"highlight_end":113}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try removing the field","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\core.rs","byte_start":14637,"byte_end":14646,"line_start":390,"line_end":390,"column_start":105,"column_end":114,"is_primary":true,"text":[{"text":"            super::PharmacyEvent::LowStockAlert { product_id, location_id, current_quantity, threshold, severity, .. } => {","highlight_start":105,"highlight_end":114}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `severity`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\core.rs:390:105\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m390\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            super::PharmacyEvent::LowStockAlert { product_id, location_id, current_quantity, threshold, severity, .. } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: try removing the field\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 60 previous errors; 17 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 60 previous errors; 17 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0057, E0106, E0119, E0282, E0428, E0432, E0433, E0599, E0616.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mSome errors have detailed explanations: E0057, E0106, E0119, E0282, E0428, E0432, E0433, E0599, E0616.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0057`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about an error, try `rustc --explain E0057`.\u001b[0m\n"}
