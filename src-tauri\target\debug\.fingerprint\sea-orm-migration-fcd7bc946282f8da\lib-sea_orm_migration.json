{"rustc": 16591470773350601817, "features": "[\"clap\", \"cli\", \"default\", \"dotenvy\", \"runtime-tokio-rustls\", \"sea-orm-cli\", \"sqlx-postgres\", \"sqlx-sqlite\"]", "declared_features": "[\"clap\", \"cli\", \"default\", \"dotenvy\", \"runtime-actix\", \"runtime-actix-native-tls\", \"runtime-actix-rustls\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"sea-orm-cli\", \"sqlite-use-returning-for-3_35\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"with-bigdecimal\", \"with-chrono\", \"with-ipnetwork\", \"with-json\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "target": 4990465507743545796, "profile": 2241668132362809309, "path": 17550927566082441788, "deps": [[291883781376595779, "sea_orm", false, 5311778270400648085], [3405707034081185165, "dotenvy", false, 4397146467665378413], [8606274917505247608, "tracing", false, 3439860457195856958], [10686516203231854806, "sea_schema", false, 15923399982697262650], [11946729385090170470, "async_trait", false, 2389517705436373950], [12141064477774631573, "sea_orm_cli", false, 11422255486452679157], [16230660778393187092, "tracing_subscriber", false, 14386935523402293211], [17612818546626403359, "clap", false, 9818290099325353580]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sea-orm-migration-fcd7bc946282f8da\\dep-lib-sea_orm_migration", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}