//! Application initialization and lifecycle management.
//!
//! This module handles the complete application lifecycle including:
//! - Startup procedures with health checks
//! - State initialization and management
//! - Graceful shutdown procedures
//! - Error recovery and cleanup

use crate::errors::{MeditrackError, MeditrackResult};
use crate::health_checks::{HealthCheckReport, perform_health_checks};
use crate::logging::{log_application_shutdown, log_application_startup, log_security_event};
use crate::state::AppState;
use tauri::Manager;
use tracing::{error, info, warn};

/// Application lifecycle manager
pub struct AppLifecycle {
    pub startup_time: std::time::Instant,
    pub health_report: Option<HealthCheckReport>,
}

impl AppLifecycle {
    pub fn new() -> Self {
        Self {
            startup_time: std::time::Instant::now(),
            health_report: None,
        }
    }

    pub fn uptime(&self) -> std::time::Duration {
        self.startup_time.elapsed()
    }
}

impl Default for AppLifecycle {
    fn default() -> Self {
        Self::new()
    }
}

/// Setup the application state with comprehensive error handling and health checks.
///
/// This function performs all necessary initialization steps including:
/// - Configuration validation
/// - System health checks
/// - Database connectivity verification
/// - State object creation with error recovery
///
/// # Returns
///
/// * `Ok(AppState)` - Successfully initialized application state
/// * `Err(MeditrackError)` - Initialization failed with specific error details
///
/// # Healthcare Compliance
///
/// This function ensures that all systems are operational before handling
/// any patient data, maintaining system reliability and data integrity.
pub async fn setup_app_state() -> MeditrackResult<AppState> {
    info!("Setting up Meditrack application state...");

    // Perform comprehensive health checks first
    let health_report = perform_health_checks().await?;

    // Check if any critical health checks failed
    if !health_report.overall_passed {
        let failed_checks: Vec<String> = health_report
            .failed_checks()
            .iter()
            .map(|check| format!("{}: {}", check.check_name, check.message))
            .collect();

        error!(
            "Critical health checks failed: {}",
            failed_checks.join("; ")
        );

        // Log security event for failed health checks
        log_security_event(
            "HEALTH_CHECK_FAILURE",
            &format!(
                "Critical health checks failed: {}",
                failed_checks.join("; ")
            ),
            None,
        );

        return Err(MeditrackError::HealthCheck(format!(
            "Health checks failed: {}",
            failed_checks.join("; ")
        )));
    }

    // Initialize application state
    match AppState::new().await {
        Ok(state) => {
            info!("Application state initialized successfully");

            // Perform post-initialization validation
            if let Err(e) = state.verify_data_integrity().await {
                warn!("Data integrity check failed: {}", e);
                // In a production system, you might want to handle this differently
                // For now, we'll log it but continue startup
            }

            // Log successful startup
            log_application_startup();

            Ok(state)
        }
        Err(e) => {
            error!("Failed to initialize application state: {}", e);

            // Log security event for state initialization failure
            log_security_event(
                "STATE_INIT_FAILURE",
                &format!("Application state initialization failed: {}", e),
                None,
            );

            Err(MeditrackError::StateInit(e))
        }
    }
}

/// Graceful cleanup function for application shutdown.
///
/// This function ensures that all resources are properly cleaned up
/// when the application shuts down, maintaining data integrity and
/// compliance with healthcare regulations.
pub async fn cleanup_resources(app_state: Option<&AppState>) -> MeditrackResult<()> {
    info!("Initiating graceful resource cleanup...");

    // Log shutdown initiation
    log_application_shutdown();

    if let Some(state) = app_state {
        // Perform graceful shutdown of application state
        if let Err(e) = state.shutdown().await {
            error!("Error during state shutdown: {}", e);
            return Err(MeditrackError::Cleanup(format!(
                "State shutdown failed: {}",
                e
            )));
        }
    }

    // Additional cleanup tasks
    cleanup_temporary_files().await?;
    flush_remaining_logs().await?;

    info!("Resource cleanup completed successfully");
    Ok(())
}

/// Clean up temporary files created during application runtime
async fn cleanup_temporary_files() -> MeditrackResult<()> {
    info!("Cleaning up temporary files...");

    let temp_dir = std::env::temp_dir();
    let _meditrack_temp_pattern = temp_dir.join("meditrack_*");

    // In a real implementation, you would clean up specific temporary files
    // created by the application. For now, this is a placeholder.

    info!("Temporary file cleanup completed");
    Ok(())
}

/// Flush any remaining logs before shutdown
async fn flush_remaining_logs() -> MeditrackResult<()> {
    info!("Flushing remaining logs...");

    // Give time for any pending log writes to complete
    tokio::time::sleep(std::time::Duration::from_millis(100)).await;

    info!("Log flushing completed");
    Ok(())
}

/// Handle application setup with proper error handling
pub async fn handle_app_setup(app_handle: tauri::AppHandle) {
    match setup_app_state().await {
        Ok(state) => {
            app_handle.manage(state);
            info!("Application setup completed successfully");

            // Log successful startup for audit trail
            info!("Meditrack application started successfully");
        }
        Err(e) => {
            error!("Application setup failed: {}", e);
            error!("Error details: {:?}", e);

            // Log failure for audit trail
            error!("Meditrack application startup failed");

            // Log security event for startup failure
            log_security_event(
                "STARTUP_FAILURE",
                &format!("Application startup failed: {}", e),
                None,
            );

            // Graceful shutdown instead of hard exit
            if let Err(cleanup_err) = cleanup_resources(None).await {
                error!("Cleanup during startup failure failed: {}", cleanup_err);
            }

            // Exit with error code
            app_handle.exit(1);
        }
    }
}

/// Handle window events for proper cleanup
pub fn handle_window_event(event: &tauri::WindowEvent) {
    match event {
        tauri::WindowEvent::Destroyed => {
            info!("Application window destroyed, initiating cleanup...");
            // Note: In a real implementation, you would access the app state here
            // and perform cleanup operations
        }
        tauri::WindowEvent::CloseRequested { .. } => {
            info!("Application close requested");
        }
        _ => {}
    }
}

/// Create Tauri application builder with all configurations
pub fn create_app_builder() -> tauri::Builder<tauri::Wry> {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .setup(|app| {
            // Initialize app state asynchronously with proper error handling
            let handle = app.handle().clone();
            tauri::async_runtime::spawn(async move {
                handle_app_setup(handle).await;
            });
            Ok(())
        })
        .on_window_event(|_window, event| {
            handle_window_event(event);
        })
        .invoke_handler(tauri::generate_handler![])
}

/// Run the Tauri application with comprehensive error handling
pub fn run_app() -> MeditrackResult<()> {
    info!("Starting Meditrack pharmacy management system...");

    let app_builder = create_app_builder();

    match app_builder.run(tauri::generate_context!()) {
        Ok(_) => {
            info!("Meditrack application shutdown completed");
            Ok(())
        }
        Err(e) => {
            error!("Tauri application runtime error: {}", e);
            error!("Application terminated unexpectedly");

            // Log security event for runtime error
            log_security_event(
                "RUNTIME_ERROR",
                &format!("Tauri runtime error: {}", e),
                None,
            );

            Err(MeditrackError::Tauri(format!("Runtime error: {}", e)))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_app_lifecycle() {
        let lifecycle = AppLifecycle::new();
        assert!(lifecycle.health_report.is_none());
    }

    #[tokio::test]
    async fn test_cleanup_resources() {
        let result = cleanup_resources(None).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_cleanup_temporary_files() {
        let result = cleanup_temporary_files().await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_flush_remaining_logs() {
        let result = flush_remaining_logs().await;
        assert!(result.is_ok());
    }

    #[test]
    fn test_handle_window_event() {
        // Test that window event handling doesn't panic
        let event = tauri::WindowEvent::Destroyed;
        handle_window_event(&event);

        // This test mainly ensures the function doesn't panic
        // In a real test, you might want to verify specific behaviors
    }
}
