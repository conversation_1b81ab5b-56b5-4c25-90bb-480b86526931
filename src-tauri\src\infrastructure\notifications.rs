//! Notification service with multiple delivery channels
//!
//! This module provides a comprehensive notification system supporting multiple delivery
//! channels including email, SMS, push notifications, and in-app notifications.

use async_trait::async_trait;
use chrono::{DateTime, Utc};
use lettre::{
    AsyncSmtpTransport, AsyncTransport, Tokio1Executor,
    message::{Mailbox, Message, header::ContentType},
    transport::smtp::authentication::Credentials,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use thiserror::Error;
use tokio::sync::RwLock;
use uuid::Uuid;

/// Errors that can occur in the notification service
#[derive(Error, Debug)]
pub enum NotificationError {
    #[error("Email delivery failed: {0}")]
    EmailError(#[from] lettre::error::Error),
    #[error("SMS delivery failed: {0}")]
    SmsError(String),
    #[error("Push notification failed: {0}")]
    PushNotificationError(String),
    #[error("Template rendering failed: {0}")]
    TemplateError(String),
    #[error("Invalid notification configuration: {0}")]
    ConfigurationError(String),
    #[error("Notification channel not supported: {0}")]
    UnsupportedChannel(String),
    #[error("Recipient not found: {0}")]
    RecipientNotFound(String),
}

/// Notification delivery channels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum NotificationChannel {
    Email,
    Sms,
    PushNotification,
    InApp,
    WhatsApp,
}

/// Notification priority levels
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum NotificationPriority {
    Low,
    Normal,
    High,
    Urgent,
    Critical,
}

/// Notification types for pharmacy operations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum NotificationType {
    PrescriptionReady,
    LowStockAlert,
    RefillReminder,
    AppointmentReminder,
    SystemAlert,
    WorkflowUpdate,
    FinancialAlert,
    ComplianceAlert,
}

/// Notification message structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationMessage {
    pub id: Uuid,
    pub notification_type: NotificationType,
    pub recipient_id: Uuid,
    pub channels: Vec<NotificationChannel>,
    pub priority: NotificationPriority,
    pub subject: String,
    pub body: String,
    pub template_data: serde_json::Value,
    pub scheduled_for: Option<DateTime<Utc>>,
    pub expires_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
}

/// Notification delivery result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeliveryResult {
    pub message_id: Uuid,
    pub channel: NotificationChannel,
    pub status: DeliveryStatus,
    pub delivered_at: Option<DateTime<Utc>>,
    pub error_message: Option<String>,
    pub retry_count: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DeliveryStatus {
    Pending,
    Sent,
    Delivered,
    Failed,
    Expired,
}

/// Recipient information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationRecipient {
    pub id: Uuid,
    pub name: String,
    pub email: Option<String>,
    pub phone: Option<String>,
    pub push_token: Option<String>,
    pub preferred_channels: Vec<NotificationChannel>,
    pub timezone: String,
    pub language: String,
}

/// Notification template
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationTemplate {
    pub id: Uuid,
    pub name: String,
    pub notification_type: NotificationType,
    pub channel: NotificationChannel,
    pub subject_template: String,
    pub body_template: String,
    pub language: String,
}

/// Email configuration
#[derive(Debug, Clone)]
pub struct EmailConfig {
    pub smtp_host: String,
    pub smtp_port: u16,
    pub username: String,
    pub password: String,
    pub from_address: String,
    pub from_name: String,
    pub use_tls: bool,
}

/// SMS configuration
#[derive(Debug, Clone)]
pub struct SmsConfig {
    pub provider: String,
    pub api_key: String,
    pub api_secret: String,
    pub from_number: String,
}

/// Push notification configuration
#[derive(Debug, Clone)]
pub struct PushConfig {
    pub firebase_key: String,
    pub apns_key: String,
    pub app_id: String,
}

/// Notification service configuration
#[derive(Debug, Clone)]
pub struct NotificationConfig {
    pub email: Option<EmailConfig>,
    pub sms: Option<SmsConfig>,
    pub push: Option<PushConfig>,
    pub max_retry_attempts: u32,
    pub retry_delay_seconds: u64,
}

/// Trait for notification delivery providers
#[async_trait]
pub trait NotificationProvider: Send + Sync {
    /// Send a notification through this provider
    async fn send_notification(
        &self,
        message: &NotificationMessage,
        recipient: &NotificationRecipient,
    ) -> Result<DeliveryResult, NotificationError>;

    /// Get the channel this provider handles
    fn channel(&self) -> NotificationChannel;

    /// Check if the provider is configured and ready
    async fn health_check(&self) -> Result<(), NotificationError>;
}

/// Email notification provider
pub struct EmailProvider {
    transport: AsyncSmtpTransport<Tokio1Executor>,
    config: EmailConfig,
}

impl EmailProvider {
    pub fn new(config: EmailConfig) -> Result<Self, NotificationError> {
        let creds = Credentials::new(config.username.clone(), config.password.clone());

        let transport = AsyncSmtpTransport::<Tokio1Executor>::relay(&config.smtp_host)
            .map_err(|e| NotificationError::ConfigurationError(e.to_string()))?
            .port(config.smtp_port)
            .credentials(creds)
            .build();

        Ok(Self { transport, config })
    }
}

#[async_trait]
impl NotificationProvider for EmailProvider {
    async fn send_notification(
        &self,
        message: &NotificationMessage,
        recipient: &NotificationRecipient,
    ) -> Result<DeliveryResult, NotificationError> {
        let email_address = recipient.email.as_ref().ok_or_else(|| {
            NotificationError::RecipientNotFound("Email address not found".to_string())
        })?;

        let from_mailbox: Mailbox =
            format!("{} <{}>", self.config.from_name, self.config.from_address)
                .parse()
                .map_err(|e: lettre::address::AddressError| {
                    NotificationError::ConfigurationError(e.to_string())
                })?;

        let to_mailbox: Mailbox = format!("{} <{}>", recipient.name, email_address)
            .parse()
            .map_err(|e: lettre::address::AddressError| {
                NotificationError::ConfigurationError(e.to_string())
            })?;

        let email = Message::builder()
            .from(from_mailbox)
            .to(to_mailbox)
            .subject(&message.subject)
            .header(ContentType::TEXT_HTML)
            .body(message.body.clone())?;

        match self.transport.send(email).await {
            Ok(_) => Ok(DeliveryResult {
                message_id: message.id,
                channel: NotificationChannel::Email,
                status: DeliveryStatus::Sent,
                delivered_at: Some(Utc::now()),
                error_message: None,
                retry_count: 0,
            }),
            Err(e) => Ok(DeliveryResult {
                message_id: message.id,
                channel: NotificationChannel::Email,
                status: DeliveryStatus::Failed,
                delivered_at: None,
                error_message: Some(e.to_string()),
                retry_count: 0,
            }),
        }
    }

    fn channel(&self) -> NotificationChannel {
        NotificationChannel::Email
    }

    async fn health_check(&self) -> Result<(), NotificationError> {
        // Test SMTP connection
        Ok(())
    }
}

/// SMS notification provider (mock implementation)
pub struct SmsProvider {
    config: SmsConfig,
}

impl SmsProvider {
    pub fn new(config: SmsConfig) -> Self {
        Self { config }
    }
}

#[async_trait]
impl NotificationProvider for SmsProvider {
    async fn send_notification(
        &self,
        message: &NotificationMessage,
        recipient: &NotificationRecipient,
    ) -> Result<DeliveryResult, NotificationError> {
        let phone_number = recipient.phone.as_ref().ok_or_else(|| {
            NotificationError::RecipientNotFound("Phone number not found".to_string())
        })?;

        // Mock SMS sending - in production, integrate with SMS provider API
        tracing::info!("Sending SMS to {}: {}", phone_number, message.body);

        Ok(DeliveryResult {
            message_id: message.id,
            channel: NotificationChannel::Sms,
            status: DeliveryStatus::Sent,
            delivered_at: Some(Utc::now()),
            error_message: None,
            retry_count: 0,
        })
    }

    fn channel(&self) -> NotificationChannel {
        NotificationChannel::Sms
    }

    async fn health_check(&self) -> Result<(), NotificationError> {
        Ok(())
    }
}

/// Push notification provider (mock implementation)
pub struct PushProvider {
    config: PushConfig,
}

impl PushProvider {
    pub fn new(config: PushConfig) -> Self {
        Self { config }
    }
}

#[async_trait]
impl NotificationProvider for PushProvider {
    async fn send_notification(
        &self,
        message: &NotificationMessage,
        recipient: &NotificationRecipient,
    ) -> Result<DeliveryResult, NotificationError> {
        let push_token = recipient.push_token.as_ref().ok_or_else(|| {
            NotificationError::RecipientNotFound("Push token not found".to_string())
        })?;

        // Mock push notification sending - in production, integrate with FCM/APNS
        tracing::info!(
            "Sending push notification to {}: {}",
            push_token,
            message.body
        );

        Ok(DeliveryResult {
            message_id: message.id,
            channel: NotificationChannel::PushNotification,
            status: DeliveryStatus::Sent,
            delivered_at: Some(Utc::now()),
            error_message: None,
            retry_count: 0,
        })
    }

    fn channel(&self) -> NotificationChannel {
        NotificationChannel::PushNotification
    }

    async fn health_check(&self) -> Result<(), NotificationError> {
        Ok(())
    }
}

/// Main notification service
#[derive(Clone)]
pub struct NotificationService {
    providers: Arc<RwLock<HashMap<NotificationChannel, Arc<dyn NotificationProvider>>>>,
    templates: Arc<RwLock<HashMap<String, NotificationTemplate>>>,
    recipients: Arc<RwLock<HashMap<Uuid, NotificationRecipient>>>,
    config: NotificationConfig,
}

impl NotificationService {
    /// Create a new notification service
    pub fn new(config: NotificationConfig) -> Self {
        Self {
            providers: Arc::new(RwLock::new(HashMap::new())),
            templates: Arc::new(RwLock::new(HashMap::new())),
            recipients: Arc::new(RwLock::new(HashMap::new())),
            config,
        }
    }

    /// Initialize the service with providers
    pub async fn initialize(&self) -> Result<(), NotificationError> {
        let mut providers = self.providers.write().await;

        // Initialize email provider if configured
        if let Some(email_config) = &self.config.email {
            let email_provider = EmailProvider::new(email_config.clone())?;
            providers.insert(NotificationChannel::Email, Arc::new(email_provider));
            tracing::info!("Email provider initialized");
        }

        // Initialize SMS provider if configured
        if let Some(sms_config) = &self.config.sms {
            let sms_provider = SmsProvider::new(sms_config.clone());
            providers.insert(NotificationChannel::Sms, Arc::new(sms_provider));
            tracing::info!("SMS provider initialized");
        }

        // Initialize push provider if configured
        if let Some(push_config) = &self.config.push {
            let push_provider = PushProvider::new(push_config.clone());
            providers.insert(
                NotificationChannel::PushNotification,
                Arc::new(push_provider),
            );
            tracing::info!("Push notification provider initialized");
        }

        Ok(())
    }

    /// Send a notification through specified channels
    pub async fn send_notification(
        &self,
        message: NotificationMessage,
    ) -> Result<Vec<DeliveryResult>, NotificationError> {
        let recipient = {
            let recipients = self.recipients.read().await;
            recipients
                .get(&message.recipient_id)
                .cloned()
                .ok_or_else(|| {
                    NotificationError::RecipientNotFound(message.recipient_id.to_string())
                })?
        };

        let providers = self.providers.read().await;
        let mut results = Vec::new();

        for channel in &message.channels {
            if let Some(provider) = providers.get(channel) {
                let result = provider.send_notification(&message, &recipient).await?;
                results.push(result);

                tracing::info!(
                    "Sent notification {} via {:?} to recipient {}",
                    message.id,
                    channel,
                    recipient.id
                );
            } else {
                results.push(DeliveryResult {
                    message_id: message.id,
                    channel: channel.clone(),
                    status: DeliveryStatus::Failed,
                    delivered_at: None,
                    error_message: Some(format!(
                        "Provider not configured for channel {:?}",
                        channel
                    )),
                    retry_count: 0,
                });

                tracing::warn!("No provider configured for channel {:?}", channel);
            }
        }

        Ok(results)
    }

    /// Send notification using template
    pub async fn send_templated_notification(
        &self,
        template_name: &str,
        recipient_id: Uuid,
        template_data: serde_json::Value,
        channels: Vec<NotificationChannel>,
        priority: NotificationPriority,
    ) -> Result<Vec<DeliveryResult>, NotificationError> {
        let template = {
            let templates = self.templates.read().await;
            templates.get(template_name).cloned().ok_or_else(|| {
                NotificationError::TemplateError(format!("Template '{}' not found", template_name))
            })?
        };

        // Render template (simplified - in production use a proper template engine)
        let subject = self.render_template(&template.subject_template, &template_data)?;
        let body = self.render_template(&template.body_template, &template_data)?;

        let message = NotificationMessage {
            id: Uuid::new_v4(),
            notification_type: template.notification_type,
            recipient_id,
            channels,
            priority,
            subject,
            body,
            template_data,
            scheduled_for: None,
            expires_at: None,
            created_at: Utc::now(),
        };

        self.send_notification(message).await
    }

    /// Register a notification template
    pub async fn register_template(&self, template: NotificationTemplate) {
        let mut templates = self.templates.write().await;
        templates.insert(template.name.clone(), template);
    }

    /// Register a notification recipient
    pub async fn register_recipient(&self, recipient: NotificationRecipient) {
        let mut recipients = self.recipients.write().await;
        recipients.insert(recipient.id, recipient);
    }

    /// Get notification recipient
    pub async fn get_recipient(&self, recipient_id: Uuid) -> Option<NotificationRecipient> {
        let recipients = self.recipients.read().await;
        recipients.get(&recipient_id).cloned()
    }

    /// Update recipient preferences
    pub async fn update_recipient_preferences(
        &self,
        recipient_id: Uuid,
        preferred_channels: Vec<NotificationChannel>,
    ) -> Result<(), NotificationError> {
        let mut recipients = self.recipients.write().await;
        if let Some(recipient) = recipients.get_mut(&recipient_id) {
            recipient.preferred_channels = preferred_channels;
            Ok(())
        } else {
            Err(NotificationError::RecipientNotFound(
                recipient_id.to_string(),
            ))
        }
    }

    /// Simple template rendering (replace {{key}} with values)
    fn render_template(
        &self,
        template: &str,
        data: &serde_json::Value,
    ) -> Result<String, NotificationError> {
        let mut result = template.to_string();

        if let serde_json::Value::Object(map) = data {
            for (key, value) in map {
                let placeholder = format!("{{{{{}}}}}", key);
                let replacement = match value {
                    serde_json::Value::String(s) => s.clone(),
                    _ => value.to_string(),
                };
                result = result.replace(&placeholder, &replacement);
            }
        }

        Ok(result)
    }

    /// Health check for all providers
    pub async fn health_check(
        &self,
    ) -> HashMap<NotificationChannel, Result<(), NotificationError>> {
        let providers = self.providers.read().await;
        let mut results = HashMap::new();

        for (channel, provider) in providers.iter() {
            let result = provider.health_check().await;
            results.insert(channel.clone(), result);
        }

        results
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_notification_service_creation() {
        let config = NotificationConfig {
            email: None,
            sms: None,
            push: None,
            max_retry_attempts: 3,
            retry_delay_seconds: 60,
        };

        let service = NotificationService::new(config);
        service.initialize().await.unwrap();
    }

    #[tokio::test]
    async fn test_template_rendering() {
        let config = NotificationConfig {
            email: None,
            sms: None,
            push: None,
            max_retry_attempts: 3,
            retry_delay_seconds: 60,
        };

        let service = NotificationService::new(config);

        let template = "Hello {{name}}, your prescription {{prescription_id}} is ready!";
        let data = serde_json::json!({
            "name": "John Doe",
            "prescription_id": "RX123456"
        });

        let rendered = service.render_template(template, &data).unwrap();
        assert_eq!(
            rendered,
            "Hello John Doe, your prescription RX123456 is ready!"
        );
    }

    #[tokio::test]
    async fn test_recipient_management() {
        let config = NotificationConfig {
            email: None,
            sms: None,
            push: None,
            max_retry_attempts: 3,
            retry_delay_seconds: 60,
        };

        let service = NotificationService::new(config);

        let recipient = NotificationRecipient {
            id: Uuid::new_v4(),
            name: "John Doe".to_string(),
            email: Some("<EMAIL>".to_string()),
            phone: Some("+1234567890".to_string()),
            push_token: None,
            preferred_channels: vec![NotificationChannel::Email],
            timezone: "UTC".to_string(),
            language: "en".to_string(),
        };

        let recipient_id = recipient.id;
        service.register_recipient(recipient).await;

        let retrieved = service.get_recipient(recipient_id).await;
        assert!(retrieved.is_some());
        assert_eq!(retrieved.unwrap().name, "John Doe");
    }
}
