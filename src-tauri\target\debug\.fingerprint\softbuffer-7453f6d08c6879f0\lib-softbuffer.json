{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 2241668132362809309, "path": 17144094705595865511, "deps": [[376837177317575824, "build_script_build", false, 4929541333867843822], [4143744114649553716, "raw_window_handle", false, 8084507311091100797], [5986029879202738730, "log", false, 13404088029148696127], [10281541584571964250, "windows_sys", false, 2119268307812781908]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-7453f6d08c6879f0\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}