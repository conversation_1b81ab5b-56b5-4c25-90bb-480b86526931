D:\programming\desktop-apps\latest-meditrack\meditrack\src-tauri\target\debug\deps\libapp_lib-a4655d903c29651b.rmeta: src\lib.rs src\app.rs src\commands\mod.rs src\errors.rs src\health_checks.rs src\infrastructure\mod.rs src\infrastructure\audit.rs src\infrastructure\cache.rs src\infrastructure\core.rs src\infrastructure\database.rs src\infrastructure\event_bus.rs src\infrastructure\examples.rs src\infrastructure\notifications.rs src\logging.rs src\security.rs src\state\mod.rs src\state\app_state.rs src\state\audit.rs src\state\circuit_breaker.rs src\state\config.rs src\state\errors.rs src\state\health.rs D:\programming\desktop-apps\latest-meditrack\meditrack\src-tauri\target\debug\build\app-dd948ee5dbc4f2d9\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

D:\programming\desktop-apps\latest-meditrack\meditrack\src-tauri\target\debug\deps\app_lib-a4655d903c29651b.d: src\lib.rs src\app.rs src\commands\mod.rs src\errors.rs src\health_checks.rs src\infrastructure\mod.rs src\infrastructure\audit.rs src\infrastructure\cache.rs src\infrastructure\core.rs src\infrastructure\database.rs src\infrastructure\event_bus.rs src\infrastructure\examples.rs src\infrastructure\notifications.rs src\logging.rs src\security.rs src\state\mod.rs src\state\app_state.rs src\state\audit.rs src\state\circuit_breaker.rs src\state\config.rs src\state\errors.rs src\state\health.rs D:\programming\desktop-apps\latest-meditrack\meditrack\src-tauri\target\debug\build\app-dd948ee5dbc4f2d9\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

src\lib.rs:
src\app.rs:
src\commands\mod.rs:
src\errors.rs:
src\health_checks.rs:
src\infrastructure\mod.rs:
src\infrastructure\audit.rs:
src\infrastructure\cache.rs:
src\infrastructure\core.rs:
src\infrastructure\database.rs:
src\infrastructure\event_bus.rs:
src\infrastructure\examples.rs:
src\infrastructure\notifications.rs:
src\logging.rs:
src\security.rs:
src\state\mod.rs:
src\state\app_state.rs:
src\state\audit.rs:
src\state\circuit_breaker.rs:
src\state\config.rs:
src\state\errors.rs:
src\state\health.rs:
D:\programming\desktop-apps\latest-meditrack\meditrack\src-tauri\target\debug\build\app-dd948ee5dbc4f2d9\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7:

# env-dep:CARGO_PKG_AUTHORS=you
# env-dep:CARGO_PKG_DESCRIPTION=A Tauri App
# env-dep:CARGO_PKG_NAME=app
# env-dep:CARGO_PKG_VERSION=0.1.0
# env-dep:OUT_DIR=D:\\programming\\desktop-apps\\latest-meditrack\\meditrack\\src-tauri\\target\\debug\\build\\app-dd948ee5dbc4f2d9\\out
