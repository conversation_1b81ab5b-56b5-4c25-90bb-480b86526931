//! Event bus system for real-time communication between modules
//!
//! This module provides a high-performance, type-safe event bus using Rust channels
//! for real-time communication between pharmacy management modules.

use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use thiserror::Error;
use tokio::sync::{RwLock, broadcast};
use uuid::Uuid;

/// Errors that can occur in the event bus system
#[derive(Error, Debug)]
pub enum EventBusError {
    #[error("Failed to send event: {0}")]
    SendError(String),
    #[error("Failed to subscribe to events: {0}")]
    SubscriptionError(String),
    #[error("Event handler error: {0}")]
    HandlerError(String),
}

/// Core pharmacy events that flow through the system
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PharmacyEvent {
    /// Stock level changes
    StockUpdated {
        product_id: Uuid,
        location_id: Uuid,
        old_quantity: rust_decimal::Decimal,
        new_quantity: rust_decimal::Decimal,
        reason: String,
        timestamp: DateTime<Utc>,
    },

    /// Low stock alerts
    LowStockAlert {
        product_id: Uuid,
        location_id: Uuid,
        current_quantity: rust_decimal::Decimal,
        threshold: rust_decimal::Decimal,
        severity: AlertSeverity,
        timestamp: DateTime<Utc>,
    },

    /// Prescription scheduling events
    PrescriptionScheduled {
        prescription_id: Uuid,
        customer_id: Uuid,
        scheduled_date: chrono::NaiveDate,
        priority: SchedulePriority,
        timestamp: DateTime<Utc>,
    },

    /// Workflow stage transitions
    WorkflowStageChanged {
        workflow_id: Uuid,
        from_stage: Option<String>,
        to_stage: String,
        duration_in_previous_stage: Option<chrono::Duration>,
        timestamp: DateTime<Utc>,
    },

    /// Financial transactions
    FinancialTransaction {
        transaction_id: Uuid,
        transaction_type: TransactionType,
        amount: rust_decimal::Decimal,
        account_id: Uuid,
        timestamp: DateTime<Utc>,
    },

    /// System health events
    SystemHealth {
        component: String,
        status: HealthStatus,
        message: String,
        timestamp: DateTime<Utc>,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertSeverity {
    Info,
    Warning,
    Critical,
    Emergency,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SchedulePriority {
    Routine,
    Urgent,
    Critical,
    Emergency,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TransactionType {
    Sale,
    Purchase,
    Adjustment,
    Transfer,
    Refund,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum HealthStatus {
    Healthy,
    Warning,
    Critical,
    Down,
}

/// Trait for handling events
#[async_trait]
pub trait EventHandler: Send + Sync {
    /// Handle an incoming event
    async fn handle_event(&self, event: &PharmacyEvent) -> Result<(), EventBusError>;

    /// Get the name of this handler for logging
    fn handler_name(&self) -> &str;
}

/// Event bus for real-time communication between modules
pub struct EventBus {
    /// Broadcast channel for events
    sender: broadcast::Sender<PharmacyEvent>,

    /// Event handlers registered by event type
    handlers: Arc<RwLock<HashMap<String, Vec<Arc<dyn EventHandler>>>>>,

    /// Event statistics
    stats: Arc<RwLock<EventStats>>,
}

#[derive(Debug, Default, Clone)]
pub struct EventStats {
    pub events_published: u64,
    pub events_processed: u64,
    pub handler_errors: u64,
}

impl EventBus {
    /// Create a new event bus with the specified channel capacity
    pub fn new(capacity: usize) -> Self {
        let (sender, _) = broadcast::channel(capacity);

        Self {
            sender,
            handlers: Arc::new(RwLock::new(HashMap::new())),
            stats: Arc::new(RwLock::new(EventStats::default())),
        }
    }

    /// Publish an event to all subscribers
    pub async fn publish(&self, event: PharmacyEvent) -> Result<(), EventBusError> {
        // Update statistics
        {
            let mut stats = self.stats.write().await;
            stats.events_published += 1;
        }

        // Send event through broadcast channel
        self.sender
            .send(event.clone())
            .map_err(|e| EventBusError::SendError(format!("Failed to broadcast event: {}", e)))?;

        // Process event through registered handlers
        self.process_event_handlers(&event).await?;

        tracing::debug!("Published event: {:?}", event);
        Ok(())
    }

    /// Subscribe to events and get a receiver
    pub fn subscribe(&self) -> broadcast::Receiver<PharmacyEvent> {
        self.sender.subscribe()
    }

    /// Register an event handler for specific event types
    pub async fn register_handler<H>(&self, handler: H, event_types: Vec<String>)
    where
        H: EventHandler + 'static,
    {
        let handler = Arc::new(handler);
        let mut handlers = self.handlers.write().await;

        for event_type in event_types {
            handlers
                .entry(event_type.clone())
                .or_insert_with(Vec::new)
                .push(handler.clone());

            tracing::info!(
                "Registered handler '{}' for event type '{}'",
                handler.handler_name(),
                event_type
            );
        }
    }

    /// Process event through registered handlers
    async fn process_event_handlers(&self, event: &PharmacyEvent) -> Result<(), EventBusError> {
        let event_type = self.get_event_type(event);
        let handlers = self.handlers.read().await;

        if let Some(event_handlers) = handlers.get(&event_type) {
            for handler in event_handlers {
                match handler.handle_event(event).await {
                    Ok(()) => {
                        let mut stats = self.stats.write().await;
                        stats.events_processed += 1;
                    }
                    Err(e) => {
                        let mut stats = self.stats.write().await;
                        stats.handler_errors += 1;

                        tracing::error!(
                            "Handler '{}' failed to process event: {}",
                            handler.handler_name(),
                            e
                        );
                    }
                }
            }
        }

        Ok(())
    }

    /// Get event type string for handler routing
    fn get_event_type(&self, event: &PharmacyEvent) -> String {
        match event {
            PharmacyEvent::StockUpdated { .. } => "stock_updated".to_string(),
            PharmacyEvent::LowStockAlert { .. } => "low_stock_alert".to_string(),
            PharmacyEvent::PrescriptionScheduled { .. } => "prescription_scheduled".to_string(),
            PharmacyEvent::WorkflowStageChanged { .. } => "workflow_stage_changed".to_string(),
            PharmacyEvent::FinancialTransaction { .. } => "financial_transaction".to_string(),
            PharmacyEvent::SystemHealth { .. } => "system_health".to_string(),
        }
    }

    /// Get event bus statistics
    pub async fn get_stats(&self) -> EventStats {
        self.stats.read().await.clone()
    }
}

impl Clone for EventBus {
    fn clone(&self) -> Self {
        Self {
            sender: self.sender.clone(),
            handlers: self.handlers.clone(),
            stats: self.stats.clone(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::{Duration, sleep};

    struct TestHandler {
        name: String,
        events_received: Arc<RwLock<Vec<PharmacyEvent>>>,
    }

    impl TestHandler {
        fn new(name: &str) -> Self {
            Self {
                name: name.to_string(),
                events_received: Arc::new(RwLock::new(Vec::new())),
            }
        }

        async fn get_received_events(&self) -> Vec<PharmacyEvent> {
            self.events_received.read().await.clone()
        }
    }

    #[async_trait]
    impl EventHandler for TestHandler {
        async fn handle_event(&self, event: &PharmacyEvent) -> Result<(), EventBusError> {
            self.events_received.write().await.push(event.clone());
            Ok(())
        }

        fn handler_name(&self) -> &str {
            &self.name
        }
    }

    #[tokio::test]
    async fn test_event_bus_publish_subscribe() {
        let event_bus = EventBus::new(100);
        let mut receiver = event_bus.subscribe();

        let test_event = PharmacyEvent::StockUpdated {
            product_id: Uuid::new_v4(),
            location_id: Uuid::new_v4(),
            old_quantity: rust_decimal::Decimal::from(10),
            new_quantity: rust_decimal::Decimal::from(5),
            reason: "Sale".to_string(),
            timestamp: Utc::now(),
        };

        // Publish event
        event_bus.publish(test_event.clone()).await.unwrap();

        // Receive event
        let received_event = receiver.recv().await.unwrap();

        match (&test_event, &received_event) {
            (
                PharmacyEvent::StockUpdated {
                    product_id: id1, ..
                },
                PharmacyEvent::StockUpdated {
                    product_id: id2, ..
                },
            ) => {
                assert_eq!(id1, id2);
            }
            _ => panic!("Event types don't match"),
        }
    }

    #[tokio::test]
    async fn test_event_handlers() {
        let event_bus = EventBus::new(100);
        let handler = TestHandler::new("test_handler");

        // Register handler
        event_bus
            .register_handler(handler.clone(), vec!["stock_updated".to_string()])
            .await;

        let test_event = PharmacyEvent::StockUpdated {
            product_id: Uuid::new_v4(),
            location_id: Uuid::new_v4(),
            old_quantity: rust_decimal::Decimal::from(10),
            new_quantity: rust_decimal::Decimal::from(5),
            reason: "Sale".to_string(),
            timestamp: Utc::now(),
        };

        // Publish event
        event_bus.publish(test_event.clone()).await.unwrap();

        // Give handler time to process
        sleep(Duration::from_millis(10)).await;

        // Check handler received event
        let received_events = handler.get_received_events().await;
        assert_eq!(received_events.len(), 1);
    }
}
