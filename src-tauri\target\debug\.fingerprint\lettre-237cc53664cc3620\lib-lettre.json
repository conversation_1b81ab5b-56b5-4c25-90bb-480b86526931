{"rustc": 16591470773350601817, "features": "[\"builder\", \"default\", \"hostname\", \"native-tls\", \"pool\", \"smtp-transport\"]", "declared_features": "[\"async-std1\", \"async-std1-rustls\", \"async-std1-rustls-tls\", \"aws-lc-rs\", \"boring-tls\", \"builder\", \"default\", \"dkim\", \"file-transport\", \"file-transport-envelope\", \"fips\", \"hostname\", \"mime03\", \"native-tls\", \"pool\", \"ring\", \"rustls\", \"rustls-native-certs\", \"rustls-platform-verifier\", \"rustls-tls\", \"sendmail-transport\", \"serde\", \"smtp-transport\", \"tokio1\", \"tokio1-boring-tls\", \"tokio1-native-tls\", \"tokio1-rustls\", \"tokio1-rustls-tls\", \"tracing\", \"web\", \"webpki-roots\"]", "target": 7615221563114883122, "profile": 4498320813676583326, "path": 12352010131121630079, "deps": [[40386456601120721, "percent_encoding", false, 10973999686076924311], [2726707743931605381, "email_address", false, 4283913278398043535], [3150220818285335163, "url", false, 3569982634382302943], [5481421284268725543, "socket2", false, 16457139467968428311], [6304235478050270880, "httpdate", false, 16081798208486058886], [6376232718484714452, "idna", false, 17277588694947190075], [7934526267265264005, "chumsky", false, 1101906695449999195], [9442026380873301823, "email_encoding", false, 8275028938251930809], [10229185211513642314, "mime", false, 1056189065056296338], [10629569228670356391, "futures_util", false, 5770148779909698394], [12285238697122577036, "fastrand", false, 13317541296791637221], [13077212702700853852, "base64", false, 10560886770982424830], [13115522443020638487, "quoted_printable", false, 11031337754777262532], [16785601910559813697, "native_tls", false, 17611468128020499438], [17164987922346580557, "hostname", false, 9428643363887937720], [18419674550203303546, "nom", false, 16795563705406339217]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\lettre-237cc53664cc3620\\dep-lib-lettre", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}