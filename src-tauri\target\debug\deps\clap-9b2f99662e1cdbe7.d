D:\programming\desktop-apps\latest-meditrack\meditrack\src-tauri\target\debug\deps\libclap-9b2f99662e1cdbe7.rmeta: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\clap-4.5.38\src\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\clap-4.5.38\src\../examples/demo.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\clap-4.5.38\src\../examples/demo.md

D:\programming\desktop-apps\latest-meditrack\meditrack\src-tauri\target\debug\deps\clap-9b2f99662e1cdbe7.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\clap-4.5.38\src\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\clap-4.5.38\src\../examples/demo.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\clap-4.5.38\src\../examples/demo.md

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\clap-4.5.38\src\lib.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\clap-4.5.38\src\../examples/demo.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\clap-4.5.38\src\../examples/demo.md:
