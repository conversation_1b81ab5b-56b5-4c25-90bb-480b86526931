{"rustc": 16591470773350601817, "features": "[\"acl\", \"aio\", \"async-trait\", \"bytes\", \"default\", \"futures-util\", \"geospatial\", \"keep-alive\", \"pin-project-lite\", \"script\", \"sha1_smol\", \"socket2\", \"streams\", \"tokio\", \"tokio-comp\", \"tokio-util\"]", "declared_features": "[\"acl\", \"ahash\", \"aio\", \"async-native-tls\", \"async-std\", \"async-std-comp\", \"async-std-native-tls-comp\", \"async-std-rustls-comp\", \"async-std-tls-comp\", \"async-trait\", \"backon\", \"bigdecimal\", \"bytes\", \"cluster\", \"cluster-async\", \"connection-manager\", \"crc16\", \"default\", \"disable-client-setinfo\", \"futures\", \"futures-rustls\", \"futures-util\", \"geospatial\", \"hashbrown\", \"json\", \"keep-alive\", \"log\", \"native-tls\", \"num-bigint\", \"pin-project-lite\", \"r2d2\", \"rand\", \"rust_decimal\", \"rustls\", \"rustls-native-certs\", \"rustls-pemfile\", \"rustls-pki-types\", \"script\", \"sentinel\", \"serde\", \"serde_json\", \"sha1_smol\", \"socket2\", \"streams\", \"tcp_nodelay\", \"tls\", \"tls-native-tls\", \"tls-rustls\", \"tls-rustls-insecure\", \"tls-rustls-webpki-roots\", \"tokio\", \"tokio-comp\", \"tokio-native-tls\", \"tokio-native-tls-comp\", \"tokio-rustls\", \"tokio-rustls-comp\", \"tokio-util\", \"uuid\", \"webpki-roots\"]", "target": 13250374349391492733, "profile": 2241668132362809309, "path": 11079304472041795356, "deps": [[40386456601120721, "percent_encoding", false, 10973999686076924311], [917570942013697716, "sha1_smol", false, 7590002215595067225], [1216309103264968120, "ryu", false, 10166734928213425651], [1288403060204016458, "tokio_util", false, 18380102494922081151], [1906322745568073236, "pin_project_lite", false, 7084418078783317499], [3150220818285335163, "url", false, 3569982634382302943], [3317542222502007281, "itertools", false, 18138754748393916194], [7695812897323945497, "itoa", false, 2950177469726742925], [8431740714262224655, "socket2", false, 8414591805367532748], [9538054652646069845, "tokio", false, 15578497443310246981], [10629569228670356391, "futures_util", false, 5770148779909698394], [11946729385090170470, "async_trait", false, 2389517705436373950], [12528732512569713347, "num_bigint", false, 17245735211893402838], [14306042979243042769, "arc_swap", false, 16090823623866969539], [16066129441945555748, "bytes", false, 18098907191387422521], [17915660048393766120, "combine", false, 4006031305825128023]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\redis-5b8333faf7ef7d1d\\dep-lib-redis", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}