//! Core infrastructure initialization and coordination
//!
//! This module provides a unified interface for initializing and managing all
//! infrastructure components of the pharmacy management system.

use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use thiserror::Error;
use chrono::{DateTime, Utc};

use super::{
    EventBus, CacheService, CacheConfig, NotificationService, NotificationConfig,
    AuditTrailManager, AuditConfig, DatabaseManager, DatabaseConfig,
    EventBusError, CacheError, NotificationError, AuditError, DatabaseError,
    event_bus::HealthStatus,
};

/// Errors that can occur during infrastructure initialization
#[derive(Error, Debug)]
pub enum InfrastructureError {
    #[error("Event bus initialization failed: {0}")]
    EventBusError(#[from] EventBusError),
    #[error("Cache service initialization failed: {0}")]
    CacheError(#[from] CacheError),
    #[error("Notification service initialization failed: {0}")]
    NotificationError(#[from] NotificationError),
    #[error("Audit service initialization failed: {0}")]
    AuditError(#[from] AuditError),
    #[error("Database initialization failed: {0}")]
    DatabaseError(#[from] DatabaseError),
    #[error("Configuration error: {0}")]
    ConfigurationError(String),
    #[error("Service dependency error: {0}")]
    DependencyError(String),
}

/// Infrastructure configuration
#[derive(Debug, Clone)]
pub struct InfrastructureConfig {
    pub database: DatabaseConfig,
    pub cache: CacheConfig,
    pub notifications: NotificationConfig,
    pub audit: AuditConfig,
    pub event_bus_capacity: usize,
    pub enable_health_checks: bool,
    pub health_check_interval_seconds: u64,
}

impl Default for InfrastructureConfig {
    fn default() -> Self {
        Self {
            database: DatabaseConfig::default(),
            cache: CacheConfig::default(),
            notifications: NotificationConfig {
                email: None,
                sms: None,
                push: None,
                max_retry_attempts: 3,
                retry_delay_seconds: 60,
            },
            audit: AuditConfig::default(),
            event_bus_capacity: 1000,
            enable_health_checks: true,
            health_check_interval_seconds: 30,
        }
    }
}

/// Core infrastructure services
pub struct InfrastructureServices {
    pub database: DatabaseManager,
    pub cache: CacheService,
    pub event_bus: EventBus,
    pub notifications: NotificationService,
    pub audit: AuditTrailManager,
    health_status: Arc<RwLock<InfrastructureHealth>>,
}

impl InfrastructureServices {
    /// Initialize all infrastructure services
    pub async fn initialize(config: InfrastructureConfig) -> Result<Self, InfrastructureError> {
        tracing::info!("Initializing infrastructure services...");
        
        // Initialize database first as other services depend on it
        tracing::info!("Initializing database manager...");
        let database = DatabaseManager::new(config.database).await?;
        
        // Initialize cache service
        tracing::info!("Initializing cache service...");
        let cache = CacheService::new(config.cache).await?;
        
        // Initialize event bus
        tracing::info!("Initializing event bus...");
        let event_bus = EventBus::new(config.event_bus_capacity);
        
        // Initialize notification service
        tracing::info!("Initializing notification service...");
        let notifications = NotificationService::new(config.notifications);
        notifications.initialize().await?;
        
        // Initialize audit trail manager
        tracing::info!("Initializing audit trail manager...");
        let audit = AuditTrailManager::new(config.audit).await?;
        
        let services = Self {
            database,
            cache,
            event_bus,
            notifications,
            audit,
            health_status: Arc::new(RwLock::new(InfrastructureHealth::default())),
        };
        
        // Set up event handlers for cross-service communication
        services.setup_event_handlers().await?;
        
        // Start health monitoring if enabled
        if config.enable_health_checks {
            services.start_health_monitoring(config.health_check_interval_seconds).await;
        }
        
        tracing::info!("Infrastructure services initialized successfully");
        Ok(services)
    }
    
    /// Set up event handlers for cross-service communication
    async fn setup_event_handlers(&self) -> Result<(), InfrastructureError> {
        // Register audit handler for all events
        let audit_handler = AuditEventHandler::new(self.audit.clone());
        self.event_bus.register_handler(
            audit_handler,
            vec![
                "stock_updated".to_string(),
                "low_stock_alert".to_string(),
                "prescription_scheduled".to_string(),
                "workflow_stage_changed".to_string(),
                "financial_transaction".to_string(),
                "system_health".to_string(),
            ]
        ).await;
        
        // Register notification handler for alerts
        let notification_handler = NotificationEventHandler::new(self.notifications.clone());
        self.event_bus.register_handler(
            notification_handler,
            vec![
                "low_stock_alert".to_string(),
                "prescription_scheduled".to_string(),
                "system_health".to_string(),
            ]
        ).await;
        
        // Register cache invalidation handler
        let cache_handler = CacheEventHandler::new(self.cache.clone());
        self.event_bus.register_handler(
            cache_handler,
            vec![
                "stock_updated".to_string(),
                "prescription_scheduled".to_string(),
                "workflow_stage_changed".to_string(),
            ]
        ).await;
        
        tracing::info!("Event handlers registered successfully");
        Ok(())
    }
    
    /// Start health monitoring for all services
    async fn start_health_monitoring(&self, interval_seconds: u64) {
        let services = self.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(
                tokio::time::Duration::from_secs(interval_seconds)
            );
            
            loop {
                interval.tick().await;
                
                let health = services.check_health().await;
                
                // Update health status
                {
                    let mut status = services.health_status.write().await;
                    *status = health.clone();
                }
                
                // Log health issues
                if !health.overall_healthy {
                    tracing::warn!("Infrastructure health check failed: {:?}", health);
                }
                
                // Publish health event
                if let Err(e) = services.event_bus.publish(super::PharmacyEvent::SystemHealth {
                    component: "infrastructure".to_string(),
                    status: if health.overall_healthy {
                        HealthStatus::Healthy
                    } else {
                        HealthStatus::Warning
                    },
                    message: format!("Health check completed: {} services healthy", 
                        health.services.iter().filter(|(_, h)| h.is_healthy).count()),
                    timestamp: Utc::now(),
                }).await {
                    tracing::error!("Failed to publish health event: {}", e);
                }
            }
        });
        
        tracing::info!("Health monitoring started with {} second interval", interval_seconds);
    }
    
    /// Perform comprehensive health check
    pub async fn check_health(&self) -> InfrastructureHealth {
        let mut health = InfrastructureHealth::default();
        
        // Check database health
        match self.database.pool().health_check().await {
            Ok(db_health) => {
                health.services.insert("database".to_string(), ServiceHealth {
                    is_healthy: db_health.is_healthy,
                    response_time_ms: db_health.response_time_ms,
                    error_message: db_health.error_message,
                    last_check: db_health.last_check,
                });
            }
            Err(e) => {
                health.services.insert("database".to_string(), ServiceHealth {
                    is_healthy: false,
                    response_time_ms: 0,
                    error_message: Some(e.to_string()),
                    last_check: Utc::now(),
                });
            }
        }
        
        // Check cache health
        match self.cache.get_stats().await {
            Ok(_) => {
                health.services.insert("cache".to_string(), ServiceHealth {
                    is_healthy: true,
                    response_time_ms: 0, // Cache stats don't include response time
                    error_message: None,
                    last_check: Utc::now(),
                });
            }
            Err(e) => {
                health.services.insert("cache".to_string(), ServiceHealth {
                    is_healthy: false,
                    response_time_ms: 0,
                    error_message: Some(e.to_string()),
                    last_check: Utc::now(),
                });
            }
        }
        
        // Check notification service health
        let notification_health = self.notifications.health_check().await;
        let notification_healthy = notification_health.values().all(|r| r.is_ok());
        health.services.insert("notifications".to_string(), ServiceHealth {
            is_healthy: notification_healthy,
            response_time_ms: 0,
            error_message: if notification_healthy {
                None
            } else {
                Some("One or more notification providers failed health check".to_string())
            },
            last_check: Utc::now(),
        });
        
        // Check event bus health (always healthy if we can access stats)
        let event_stats = self.event_bus.get_stats().await;
        health.services.insert("event_bus".to_string(), ServiceHealth {
            is_healthy: true,
            response_time_ms: 0,
            error_message: None,
            last_check: Utc::now(),
        });
        
        // Check audit service health
        let audit_stats = self.audit.get_stats().await;
        health.services.insert("audit".to_string(), ServiceHealth {
            is_healthy: true,
            response_time_ms: 0,
            error_message: None,
            last_check: Utc::now(),
        });
        
        // Determine overall health
        health.overall_healthy = health.services.values().all(|s| s.is_healthy);
        health.last_check = Utc::now();
        
        health
    }
    
    /// Get current health status
    pub async fn get_health_status(&self) -> InfrastructureHealth {
        self.health_status.read().await.clone()
    }
    
    /// Graceful shutdown of all services
    pub async fn shutdown(&self) -> Result<(), InfrastructureError> {
        tracing::info!("Shutting down infrastructure services...");
        
        // Flush any pending audit events
        if let Err(e) = self.audit.flush_events().await {
            tracing::error!("Failed to flush audit events during shutdown: {}", e);
        }
        
        // Close database connections
        if let Err(e) = self.database.close().await {
            tracing::error!("Failed to close database connections: {}", e);
        }
        
        tracing::info!("Infrastructure services shutdown completed");
        Ok(())
    }
}

impl Clone for InfrastructureServices {
    fn clone(&self) -> Self {
        Self {
            database: self.database.clone(),
            cache: self.cache.clone(),
            event_bus: self.event_bus.clone(),
            notifications: self.notifications.clone(),
            audit: self.audit.clone(),
            health_status: self.health_status.clone(),
        }
    }
}

/// Infrastructure health status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InfrastructureHealth {
    pub overall_healthy: bool,
    pub services: std::collections::HashMap<String, ServiceHealth>,
    pub last_check: DateTime<Utc>,
}

impl Default for InfrastructureHealth {
    fn default() -> Self {
        Self {
            overall_healthy: false,
            services: std::collections::HashMap::new(),
            last_check: Utc::now(),
        }
    }
}

/// Individual service health status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceHealth {
    pub is_healthy: bool,
    pub response_time_ms: u64,
    pub error_message: Option<String>,
    pub last_check: DateTime<Utc>,
}

// Event handlers for cross-service communication

/// Audit event handler
struct AuditEventHandler {
    audit: AuditTrailManager,
}

impl AuditEventHandler {
    fn new(audit: AuditTrailManager) -> Self {
        Self { audit }
    }
}

#[async_trait::async_trait]
impl super::EventHandler for AuditEventHandler {
    async fn handle_event(&self, event: &super::PharmacyEvent) -> Result<(), super::EventBusError> {
        use super::{AuditEvent, AuditEventType, AuditSeverity};
        
        let audit_event = match event {
            super::PharmacyEvent::StockUpdated { product_id, location_id, old_quantity, new_quantity, reason, .. } => {
                AuditEvent::new(
                    AuditEventType::StockAdjusted,
                    AuditSeverity::Info,
                    "stock_updated".to_string(),
                    format!("Stock updated for product {} at location {}: {} -> {}, reason: {}", 
                        product_id, location_id, old_quantity, new_quantity, reason),
                ).with_resource("product".to_string(), *product_id)
                 .with_location(*location_id)
            }
            super::PharmacyEvent::LowStockAlert { product_id, location_id, current_quantity, threshold, severity, .. } => {
                AuditEvent::new(
                    AuditEventType::ComplianceCheck,
                    AuditSeverity::Warning,
                    "low_stock_alert".to_string(),
                    format!("Low stock alert for product {} at location {}: {} below threshold {}", 
                        product_id, location_id, current_quantity, threshold),
                ).with_resource("product".to_string(), *product_id)
                 .with_location(*location_id)
            }
            super::PharmacyEvent::PrescriptionScheduled { prescription_id, customer_id, scheduled_date, .. } => {
                AuditEvent::new(
                    AuditEventType::PrescriptionCreated,
                    AuditSeverity::Info,
                    "prescription_scheduled".to_string(),
                    format!("Prescription {} scheduled for customer {} on {}", 
                        prescription_id, customer_id, scheduled_date),
                ).with_resource("prescription".to_string(), *prescription_id)
            }
            _ => return Ok(()), // Skip other events
        };
        
        self.audit.log_event(audit_event).await
            .map_err(|e| super::EventBusError::HandlerError(e.to_string()))?;
        
        Ok(())
    }
    
    fn handler_name(&self) -> &str {
        "audit_event_handler"
    }
}

/// Notification event handler
struct NotificationEventHandler {
    notifications: NotificationService,
}

impl NotificationEventHandler {
    fn new(notifications: NotificationService) -> Self {
        Self { notifications }
    }
}

#[async_trait::async_trait]
impl super::EventHandler for NotificationEventHandler {
    async fn handle_event(&self, event: &super::PharmacyEvent) -> Result<(), super::EventBusError> {
        // Handle notification-worthy events
        match event {
            super::PharmacyEvent::LowStockAlert { product_id, current_quantity, threshold, .. } => {
                // In a real implementation, we would look up relevant users to notify
                tracing::info!("Would send low stock notification for product {}: {} below {}", 
                    product_id, current_quantity, threshold);
            }
            super::PharmacyEvent::PrescriptionScheduled { prescription_id, customer_id, .. } => {
                tracing::info!("Would send prescription ready notification for prescription {} to customer {}", 
                    prescription_id, customer_id);
            }
            _ => {} // Skip other events
        }
        
        Ok(())
    }
    
    fn handler_name(&self) -> &str {
        "notification_event_handler"
    }
}

/// Cache event handler for invalidation
struct CacheEventHandler {
    cache: CacheService,
}

impl CacheEventHandler {
    fn new(cache: CacheService) -> Self {
        Self { cache }
    }
}

#[async_trait::async_trait]
impl super::EventHandler for CacheEventHandler {
    async fn handle_event(&self, event: &super::PharmacyEvent) -> Result<(), super::EventBusError> {
        // Handle cache invalidation for relevant events
        match event {
            super::PharmacyEvent::StockUpdated { product_id, location_id, .. } => {
                let cache_key = format!("stock:{}:{}", product_id, location_id);
                if let Err(e) = self.cache.delete(&cache_key).await {
                    tracing::warn!("Failed to invalidate cache key {}: {}", cache_key, e);
                }
            }
            super::PharmacyEvent::PrescriptionScheduled { prescription_id, .. } => {
                let cache_key = format!("prescription:{}", prescription_id);
                if let Err(e) = self.cache.delete(&cache_key).await {
                    tracing::warn!("Failed to invalidate cache key {}: {}", cache_key, e);
                }
            }
            _ => {} // Skip other events
        }
        
        Ok(())
    }
    
    fn handler_name(&self) -> &str {
        "cache_event_handler"
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_infrastructure_config_default() {
        let config = InfrastructureConfig::default();
        assert_eq!(config.event_bus_capacity, 1000);
        assert!(config.enable_health_checks);
        assert_eq!(config.health_check_interval_seconds, 30);
    }
    
    #[test]
    fn test_infrastructure_health_default() {
        let health = InfrastructureHealth::default();
        assert!(!health.overall_healthy);
        assert!(health.services.is_empty());
    }
    
    #[test]
    fn test_service_health() {
        let health = ServiceHealth {
            is_healthy: true,
            response_time_ms: 50,
            error_message: None,
            last_check: Utc::now(),
        };
        
        assert!(health.is_healthy);
        assert_eq!(health.response_time_ms, 50);
        assert!(health.error_message.is_none());
    }
}