{"rustc": 16591470773350601817, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 2241668132362809309, "path": 12577231443999000584, "deps": [[966925859616469517, "ahash", false, 15770294250106622923], [9150530836556604396, "allocator_api2", false, 16629995443191540057]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-fb8f04298938511c\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}