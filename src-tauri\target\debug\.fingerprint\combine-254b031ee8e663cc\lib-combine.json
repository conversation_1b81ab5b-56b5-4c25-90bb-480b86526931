{"rustc": 16591470773350601817, "features": "[\"alloc\", \"bytes\", \"futures-core-03\", \"pin-project-lite\", \"std\", \"tokio\", \"tokio-dep\", \"tokio-util\"]", "declared_features": "[\"alloc\", \"bytes\", \"bytes_05\", \"default\", \"futures-03\", \"futures-core-03\", \"futures-io-03\", \"mp4\", \"pin-project\", \"pin-project-lite\", \"regex\", \"std\", \"tokio\", \"tokio-02\", \"tokio-02-dep\", \"tokio-03\", \"tokio-03-dep\", \"tokio-dep\", \"tokio-util\"]", "target": 2090804380371586739, "profile": 2241668132362809309, "path": 4076771611687678666, "deps": [[1288403060204016458, "tokio_util", false, 18380102494922081151], [1906322745568073236, "pin_project_lite", false, 7084418078783317499], [3129130049864710036, "memchr", false, 10024402093055877215], [7620660491849607393, "futures_core_03", false, 17451578802806441489], [9538054652646069845, "tokio_dep", false, 15578497443310246981], [16066129441945555748, "bytes", false, 18098907191387422521]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\combine-254b031ee8e663cc\\dep-lib-combine", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}