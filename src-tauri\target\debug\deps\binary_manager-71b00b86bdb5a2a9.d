D:\programming\desktop-apps\latest-meditrack\meditrack\src-tauri\target\debug\deps\libbinary_manager-71b00b86bdb5a2a9.rmeta: crates\binary_manager\src\lib.rs Cargo.toml

D:\programming\desktop-apps\latest-meditrack\meditrack\src-tauri\target\debug\deps\binary_manager-71b00b86bdb5a2a9.d: crates\binary_manager\src\lib.rs Cargo.toml

crates\binary_manager\src\lib.rs:
Cargo.toml:

# env-dep:CLIPPY_ARGS=
# env-dep:CLIPPY_CONF_DIR
