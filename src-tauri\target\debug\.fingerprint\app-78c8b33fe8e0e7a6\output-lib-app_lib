{"$message_type":"diagnostic","message":"unresolved imports `super::AlertSeverity`, `super::SchedulePriority`, `super::TransactionType`, `super::NotificationPriority`, `super::NotificationType`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":349,"byte_end":362,"line_start":12,"line_end":12,"column_start":20,"column_end":33,"is_primary":true,"text":[{"text":"    PharmacyEvent, AlertSeverity, SchedulePriority, TransactionType,","highlight_start":20,"highlight_end":33}],"label":"no `AlertSeverity` in `infrastructure`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\examples.rs","byte_start":364,"byte_end":380,"line_start":12,"line_end":12,"column_start":35,"column_end":51,"is_primary":true,"text":[{"text":"    PharmacyEvent, AlertSeverity, SchedulePriority, TransactionType,","highlight_start":35,"highlight_end":51}],"label":"no `SchedulePriority` in `infrastructure`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\examples.rs","byte_start":382,"byte_end":397,"line_start":12,"line_end":12,"column_start":53,"column_end":68,"is_primary":true,"text":[{"text":"    PharmacyEvent, AlertSeverity, SchedulePriority, TransactionType,","highlight_start":53,"highlight_end":68}],"label":"no `TransactionType` in `infrastructure`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\examples.rs","byte_start":482,"byte_end":502,"line_start":14,"line_end":14,"column_start":47,"column_end":67,"is_primary":true,"text":[{"text":"    NotificationMessage, NotificationChannel, NotificationPriority, NotificationType,","highlight_start":47,"highlight_end":67}],"label":"no `NotificationPriority` in `infrastructure`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\examples.rs","byte_start":504,"byte_end":520,"line_start":14,"line_end":14,"column_start":69,"column_end":85,"is_primary":true,"text":[{"text":"    NotificationMessage, NotificationChannel, NotificationPriority, NotificationType,","highlight_start":69,"highlight_end":85}],"label":"no `NotificationType` in `infrastructure`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing this enum instead:\ncrate::infrastructure::event_bus::AlertSeverity","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"consider importing this enum instead:\ncrate::infrastructure::event_bus::SchedulePriority","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"consider importing this enum instead:\ncrate::infrastructure::event_bus::TransactionType","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"consider importing this enum instead:\ncrate::infrastructure::notifications::NotificationPriority","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"consider importing this enum instead:\ncrate::infrastructure::notifications::NotificationType","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"a similar name exists in the module","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":349,"byte_end":362,"line_start":12,"line_end":12,"column_start":20,"column_end":33,"is_primary":true,"text":[{"text":"    PharmacyEvent, AlertSeverity, SchedulePriority, TransactionType,","highlight_start":20,"highlight_end":33}],"label":null,"suggested_replacement":"AuditSeverity","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"a similar name exists in the module","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":482,"byte_end":502,"line_start":14,"line_end":14,"column_start":47,"column_end":67,"is_primary":true,"text":[{"text":"    NotificationMessage, NotificationChannel, NotificationPriority, NotificationType,","highlight_start":47,"highlight_end":67}],"label":null,"suggested_replacement":"NotificationError","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"a similar name exists in the module","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":504,"byte_end":520,"line_start":14,"line_end":14,"column_start":69,"column_end":85,"is_primary":true,"text":[{"text":"    NotificationMessage, NotificationChannel, NotificationPriority, NotificationType,","highlight_start":69,"highlight_end":85}],"label":null,"suggested_replacement":"notifications","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unresolved imports `super::AlertSeverity`, `super::SchedulePriority`, `super::TransactionType`, `super::NotificationPriority`, `super::NotificationType`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\examples.rs:12:20\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    PharmacyEvent, AlertSeverity, SchedulePriority, TransactionType,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `TransactionType` in `infrastructure`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `SchedulePriority` in `infrastructure`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `AlertSeverity` in `infrastructure`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    AuditEventType, AuditSeverity,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    NotificationMessage, NotificationChannel, NotificationPriority, NotificationType,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `NotificationType` in `infrastructure`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `NotificationPriority` in `infrastructure`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider importing this enum instead:\u001b[0m\n\u001b[0m           crate::infrastructure::event_bus::AlertSeverity\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider importing this enum instead:\u001b[0m\n\u001b[0m           crate::infrastructure::event_bus::SchedulePriority\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider importing this enum instead:\u001b[0m\n\u001b[0m           crate::infrastructure::event_bus::TransactionType\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider importing this enum instead:\u001b[0m\n\u001b[0m           crate::infrastructure::notifications::NotificationPriority\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider importing this enum instead:\u001b[0m\n\u001b[0m           crate::infrastructure::notifications::NotificationType\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: a similar name exists in the module\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    PharmacyEvent, \u001b[0m\u001b[0m\u001b[38;5;9mAlertSeverity\u001b[0m\u001b[0m, SchedulePriority, TransactionType,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    PharmacyEvent, \u001b[0m\u001b[0m\u001b[38;5;10mAuditSeverity\u001b[0m\u001b[0m, SchedulePriority, TransactionType,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: a similar name exists in the module\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    NotificationMessage, NotificationChannel, \u001b[0m\u001b[0m\u001b[38;5;9mNotificationPriority\u001b[0m\u001b[0m, NotificationType,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    NotificationMessage, NotificationChannel, \u001b[0m\u001b[0m\u001b[38;5;10mNotificationError\u001b[0m\u001b[0m, NotificationType,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: a similar name exists in the module\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    NotificationMessage, NotificationChannel, NotificationPriority, \u001b[0m\u001b[0m\u001b[38;5;9mNotificationType\u001b[0m\u001b[0m,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    NotificationMessage, NotificationChannel, NotificationPriority, \u001b[0m\u001b[0m\u001b[38;5;10mnotifications\u001b[0m\u001b[0m,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Set`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\audit.rs","byte_start":379,"byte_end":382,"line_start":7,"line_end":7,"column_start":76,"column_end":79,"is_primary":true,"text":[{"text":"use sea_orm::{ActiveModelTrait, Database, DatabaseConnection, EntityTrait, Set};","highlight_start":76,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\audit.rs","byte_start":377,"byte_end":382,"line_start":7,"line_end":7,"column_start":74,"column_end":79,"is_primary":true,"text":[{"text":"use sea_orm::{ActiveModelTrait, Database, DatabaseConnection, EntityTrait, Set};","highlight_start":74,"highlight_end":79}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Set`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\audit.rs:7:76\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse sea_orm::{ActiveModelTrait, Database, DatabaseConnection, EntityTrait, Set};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `RedisResult`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":367,"byte_end":378,"line_start":7,"line_end":7,"column_start":58,"column_end":69,"is_primary":true,"text":[{"text":"use redis::{AsyncCommands, Client, Commands, Connection, RedisResult};","highlight_start":58,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":365,"byte_end":378,"line_start":7,"line_end":7,"column_start":56,"column_end":69,"is_primary":true,"text":[{"text":"use redis::{AsyncCommands, Client, Commands, Connection, RedisResult};","highlight_start":56,"highlight_end":69}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `RedisResult`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:7:58\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse redis::{AsyncCommands, Client, Commands, Connection, RedisResult};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `ConnectionTrait`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\database.rs","byte_start":320,"byte_end":335,"line_start":8,"line_end":8,"column_start":21,"column_end":36,"is_primary":true,"text":[{"text":"    ConnectOptions, ConnectionTrait, Database, DatabaseConnection, DatabaseTransaction, DbErr,","highlight_start":21,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\database.rs","byte_start":318,"byte_end":335,"line_start":8,"line_end":8,"column_start":19,"column_end":36,"is_primary":true,"text":[{"text":"    ConnectOptions, ConnectionTrait, Database, DatabaseConnection, DatabaseTransaction, DbErr,","highlight_start":19,"highlight_end":36}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `ConnectionTrait`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\database.rs:8:21\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ConnectOptions, ConnectionTrait, Database, DatabaseConnection, DatabaseTransaction, DbErr,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `uuid::Uuid`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\database.rs","byte_start":561,"byte_end":571,"line_start":16,"line_end":16,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"use uuid::Uuid;","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\database.rs","byte_start":557,"byte_end":574,"line_start":16,"line_end":17,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use uuid::Uuid;","highlight_start":1,"highlight_end":16},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `uuid::Uuid`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\database.rs:16:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse uuid::Uuid;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `AuditSeverity`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":420,"byte_end":433,"line_start":13,"line_end":13,"column_start":21,"column_end":34,"is_primary":true,"text":[{"text":"    AuditEventType, AuditSeverity,","highlight_start":21,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\examples.rs","byte_start":418,"byte_end":433,"line_start":13,"line_end":13,"column_start":19,"column_end":34,"is_primary":true,"text":[{"text":"    AuditEventType, AuditSeverity,","highlight_start":19,"highlight_end":34}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `AuditSeverity`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\examples.rs:13:21\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    AuditEventType, AuditSeverity,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ComplianceReportType` and `ComplianceReport`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\mod.rs","byte_start":685,"byte_end":701,"line_start":20,"line_end":20,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"    ComplianceReport, ComplianceReportType,","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\mod.rs","byte_start":703,"byte_end":723,"line_start":20,"line_end":20,"column_start":23,"column_end":43,"is_primary":true,"text":[{"text":"    ComplianceReport, ComplianceReportType,","highlight_start":23,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\mod.rs","byte_start":678,"byte_end":723,"line_start":19,"line_end":20,"column_start":90,"column_end":43,"is_primary":true,"text":[{"text":"    AuditConfig, AuditError, AuditEvent, AuditEventType, AuditSeverity, AuditTrailManager,","highlight_start":90,"highlight_end":91},{"text":"    ComplianceReport, ComplianceReportType,","highlight_start":1,"highlight_end":43}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `ComplianceReportType` and `ComplianceReport`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\mod.rs:20:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m20\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ComplianceReport, ComplianceReportType,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `SessionManager`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\mod.rs","byte_start":785,"byte_end":799,"line_start":22,"line_end":22,"column_start":56,"column_end":70,"is_primary":true,"text":[{"text":"pub use cache::{CacheConfig, CacheError, CacheService, SessionManager};","highlight_start":56,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\mod.rs","byte_start":783,"byte_end":799,"line_start":22,"line_end":22,"column_start":54,"column_end":70,"is_primary":true,"text":[{"text":"pub use cache::{CacheConfig, CacheError, CacheService, SessionManager};","highlight_start":54,"highlight_end":70}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `SessionManager`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\mod.rs:22:56\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use cache::{CacheConfig, CacheError, CacheService, SessionManager};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `InfrastructureError`, `InfrastructureHealth`, and `ServiceHealth`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\mod.rs","byte_start":846,"byte_end":865,"line_start":24,"line_end":24,"column_start":27,"column_end":46,"is_primary":true,"text":[{"text":"    InfrastructureConfig, InfrastructureError, InfrastructureHealth, InfrastructureServices,","highlight_start":27,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\mod.rs","byte_start":867,"byte_end":887,"line_start":24,"line_end":24,"column_start":48,"column_end":68,"is_primary":true,"text":[{"text":"    InfrastructureConfig, InfrastructureError, InfrastructureHealth, InfrastructureServices,","highlight_start":48,"highlight_end":68}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\mod.rs","byte_start":918,"byte_end":931,"line_start":25,"line_end":25,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    ServiceHealth,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\mod.rs","byte_start":844,"byte_end":887,"line_start":24,"line_end":24,"column_start":25,"column_end":68,"is_primary":true,"text":[{"text":"    InfrastructureConfig, InfrastructureError, InfrastructureHealth, InfrastructureServices,","highlight_start":25,"highlight_end":68}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\infrastructure\\mod.rs","byte_start":911,"byte_end":931,"line_start":24,"line_end":25,"column_start":92,"column_end":18,"is_primary":true,"text":[{"text":"    InfrastructureConfig, InfrastructureError, InfrastructureHealth, InfrastructureServices,","highlight_start":92,"highlight_end":93},{"text":"    ServiceHealth,","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `InfrastructureError`, `InfrastructureHealth`, and `ServiceHealth`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\mod.rs:24:27\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    InfrastructureConfig, InfrastructureError, InfrastructureHealth, InfrastructureServices,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ServiceHealth,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ConnectionPool`, `DatabaseStats`, `MigrationManager`, and `TransactionManager`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\mod.rs","byte_start":963,"byte_end":977,"line_start":28,"line_end":28,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    ConnectionPool, DatabaseConfig, DatabaseError, DatabaseManager, DatabaseStats,","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\mod.rs","byte_start":1027,"byte_end":1040,"line_start":28,"line_end":28,"column_start":69,"column_end":82,"is_primary":true,"text":[{"text":"    ConnectionPool, DatabaseConfig, DatabaseError, DatabaseManager, DatabaseStats,","highlight_start":69,"highlight_end":82}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\mod.rs","byte_start":1047,"byte_end":1063,"line_start":29,"line_end":29,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"    MigrationManager, TransactionManager,","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\mod.rs","byte_start":1065,"byte_end":1083,"line_start":29,"line_end":29,"column_start":23,"column_end":41,"is_primary":true,"text":[{"text":"    MigrationManager, TransactionManager,","highlight_start":23,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\mod.rs","byte_start":963,"byte_end":979,"line_start":28,"line_end":28,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"    ConnectionPool, DatabaseConfig, DatabaseError, DatabaseManager, DatabaseStats,","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\infrastructure\\mod.rs","byte_start":1025,"byte_end":1083,"line_start":28,"line_end":29,"column_start":67,"column_end":41,"is_primary":true,"text":[{"text":"    ConnectionPool, DatabaseConfig, DatabaseError, DatabaseManager, DatabaseStats,","highlight_start":67,"highlight_end":83},{"text":"    MigrationManager, TransactionManager,","highlight_start":1,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `ConnectionPool`, `DatabaseStats`, `MigrationManager`, and `TransactionManager`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\mod.rs:28:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ConnectionPool, DatabaseConfig, DatabaseError, DatabaseManager, DatabaseStats,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m29\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    MigrationManager, TransactionManager,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `EmailProvider`, `NotificationProvider`, `PushProvider`, and `SmsProvider`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\mod.rs","byte_start":1196,"byte_end":1209,"line_start":33,"line_end":33,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    EmailProvider, NotificationChannel, NotificationConfig, NotificationError, NotificationMessage,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\mod.rs","byte_start":1297,"byte_end":1317,"line_start":34,"line_end":34,"column_start":5,"column_end":25,"is_primary":true,"text":[{"text":"    NotificationProvider, NotificationRecipient, NotificationService, PushProvider, SmsProvider,","highlight_start":5,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\mod.rs","byte_start":1363,"byte_end":1375,"line_start":34,"line_end":34,"column_start":71,"column_end":83,"is_primary":true,"text":[{"text":"    NotificationProvider, NotificationRecipient, NotificationService, PushProvider, SmsProvider,","highlight_start":71,"highlight_end":83}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\mod.rs","byte_start":1377,"byte_end":1388,"line_start":34,"line_end":34,"column_start":85,"column_end":96,"is_primary":true,"text":[{"text":"    NotificationProvider, NotificationRecipient, NotificationService, PushProvider, SmsProvider,","highlight_start":85,"highlight_end":96}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\mod.rs","byte_start":1196,"byte_end":1211,"line_start":33,"line_end":33,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    EmailProvider, NotificationChannel, NotificationConfig, NotificationError, NotificationMessage,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\infrastructure\\mod.rs","byte_start":1290,"byte_end":1317,"line_start":33,"line_end":34,"column_start":99,"column_end":25,"is_primary":true,"text":[{"text":"    EmailProvider, NotificationChannel, NotificationConfig, NotificationError, NotificationMessage,","highlight_start":99,"highlight_end":100},{"text":"    NotificationProvider, NotificationRecipient, NotificationService, PushProvider, SmsProvider,","highlight_start":1,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\infrastructure\\mod.rs","byte_start":1361,"byte_end":1388,"line_start":34,"line_end":34,"column_start":69,"column_end":96,"is_primary":true,"text":[{"text":"    NotificationProvider, NotificationRecipient, NotificationService, PushProvider, SmsProvider,","highlight_start":69,"highlight_end":96}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `EmailProvider`, `NotificationProvider`, `PushProvider`, and `SmsProvider`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\mod.rs:33:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    EmailProvider, NotificationChannel, NotificationConfig, NotificationError, NotificationMessage,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m34\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    NotificationProvider, NotificationRecipient, NotificationService, PushProvider, SmsProvider,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":3134,"byte_end":3154,"line_start":101,"line_end":101,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(deprecated)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:101:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m101\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(deprecated)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":3535,"byte_end":3555,"line_start":114,"line_end":114,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:114:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m114\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":4163,"byte_end":4183,"line_start":133,"line_end":133,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:133:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m133\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":4507,"byte_end":4527,"line_start":142,"line_end":142,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:142:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m142\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":4785,"byte_end":4805,"line_start":150,"line_end":150,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:150:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m150\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":5136,"byte_end":5156,"line_start":161,"line_end":161,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:161:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m161\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":5810,"byte_end":5830,"line_start":183,"line_end":183,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:183:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m183\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":6210,"byte_end":6230,"line_start":195,"line_end":195,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:195:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m195\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":6590,"byte_end":6610,"line_start":207,"line_end":207,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:207:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m207\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":6977,"byte_end":6997,"line_start":223,"line_end":223,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:223:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m223\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":7552,"byte_end":7572,"line_start":241,"line_end":241,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:241:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m241\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":7877,"byte_end":7897,"line_start":252,"line_end":252,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:252:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m252\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":8327,"byte_end":8347,"line_start":266,"line_end":266,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:266:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m266\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":8651,"byte_end":8671,"line_start":275,"line_end":275,"column_start":36,"column_end":56,"is_primary":true,"text":[{"text":"        let mut conn = self.client.get_async_connection().await?;","highlight_start":36,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated method `redis::Client::get_async_connection`: aio::Connection is deprecated. Use client::get_multiplexed_async_connection instead.\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:275:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m275\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut conn = self.client.get_async_connection().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"future cannot be sent between threads safely","code":null,"level":"error","spans":[{"file_name":"src\\infrastructure\\database.rs","byte_start":10521,"byte_end":10787,"line_start":326,"line_end":335,"column_start":13,"column_end":15,"is_primary":true,"text":[{"text":"            Box::pin(async move {","highlight_start":13,"highlight_end":34},{"text":"                let mut results = Vec::new();","highlight_start":1,"highlight_end":46},{"text":"","highlight_start":1,"highlight_end":1},{"text":"                for operation in operations {","highlight_start":1,"highlight_end":46},{"text":"                    operation(txn).await?;","highlight_start":1,"highlight_end":43},{"text":"                    results.push(());","highlight_start":1,"highlight_end":38},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"","highlight_start":1,"highlight_end":1},{"text":"                Ok(results)","highlight_start":1,"highlight_end":28},{"text":"            })","highlight_start":1,"highlight_end":15}],"label":"future created by async block is not `Send`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"captured value is not `Send`","code":null,"level":"note","spans":[{"file_name":"src\\infrastructure\\database.rs","byte_start":10626,"byte_end":10636,"line_start":329,"line_end":329,"column_start":34,"column_end":44,"is_primary":true,"text":[{"text":"                for operation in operations {","highlight_start":34,"highlight_end":44}],"label":"has type `Vec<F>` which is not `Send`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"required for the cast from `Pin<Box<{async block@src\\infrastructure\\database.rs:326:22: 326:32}>>` to `Pin<Box<dyn Future<Output = Result<Vec<()>, ...>> + Send>>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the full name for the type has been written to 'D:\\programming\\desktop-apps\\latest-meditrack\\meditrack\\src-tauri\\target\\debug\\deps\\app_lib-78c8b33fe8e0e7a6.long-type-14442090844263682603.txt'","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider using `--verbose` to print the full type name to the console","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider further restricting type parameter `F` with trait `Send`","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\database.rs","byte_start":10457,"byte_end":10457,"line_start":323,"line_end":323,"column_start":10,"column_end":10,"is_primary":true,"text":[{"text":"        >,","highlight_start":10,"highlight_end":10}],"label":null,"suggested_replacement":" + std::marker::Send","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: future cannot be sent between threads safely\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\database.rs:326:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m326\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m            Box::pin(async move {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m327\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                let mut results = Vec::new();\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m328\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m329\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                for operation in operations {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m334\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                Ok(results)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m335\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            })\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|______________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfuture created by async block is not `Send`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: captured value is not `Send`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\database.rs:329:34\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m329\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                for operation in operations {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mhas type `Vec<F>` which is not `Send`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: required for the cast from `Pin<Box<{async block@src\\infrastructure\\database.rs:326:22: 326:32}>>` to `Pin<Box<dyn Future<Output = Result<Vec<()>, ...>> + Send>>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the full name for the type has been written to 'D:\\programming\\desktop-apps\\latest-meditrack\\meditrack\\src-tauri\\target\\debug\\deps\\app_lib-78c8b33fe8e0e7a6.long-type-14442090844263682603.txt'\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `--verbose` to print the full type name to the console\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider further restricting type parameter `F` with trait `Send`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m323\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m        >\u001b[0m\u001b[0m\u001b[38;5;10m + std::marker::Send\u001b[0m\u001b[0m,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[38;5;10m+++++++++++++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `clone` found for enum `DatabaseConnection` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\audit.rs","byte_start":16210,"byte_end":16215,"line_start":548,"line_end":548,"column_start":25,"column_end":30,"is_primary":true,"text":[{"text":"            db: self.db.clone(),","highlight_start":25,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `close` with a similar name","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\audit.rs","byte_start":16210,"byte_end":16215,"line_start":548,"line_end":548,"column_start":25,"column_end":30,"is_primary":true,"text":[{"text":"            db: self.db.clone(),","highlight_start":25,"highlight_end":30}],"label":null,"suggested_replacement":"close","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `clone` found for enum `DatabaseConnection` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\audit.rs:548:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m548\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            db: self.db.clone(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `close` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m548\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            db: self.db.\u001b[0m\u001b[0m\u001b[38;5;9mclone\u001b[0m\u001b[0m(),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m548\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            db: self.db.\u001b[0m\u001b[0m\u001b[38;5;10mclose\u001b[0m\u001b[0m(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `ActiveModelTrait`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\audit.rs","byte_start":318,"byte_end":334,"line_start":7,"line_end":7,"column_start":15,"column_end":31,"is_primary":true,"text":[{"text":"use sea_orm::{ActiveModelTrait, Database, DatabaseConnection, EntityTrait, Set};","highlight_start":15,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `ActiveModelTrait`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\audit.rs:7:15\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse sea_orm::{ActiveModelTrait, Database, DatabaseConnection, EntityTrait, Set};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `EntityTrait`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\audit.rs","byte_start":366,"byte_end":377,"line_start":7,"line_end":7,"column_start":63,"column_end":74,"is_primary":true,"text":[{"text":"use sea_orm::{ActiveModelTrait, Database, DatabaseConnection, EntityTrait, Set};","highlight_start":63,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `EntityTrait`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\audit.rs:7:63\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse sea_orm::{ActiveModelTrait, Database, DatabaseConnection, EntityTrait, Set};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Commands`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\cache.rs","byte_start":345,"byte_end":353,"line_start":7,"line_end":7,"column_start":36,"column_end":44,"is_primary":true,"text":[{"text":"use redis::{AsyncCommands, Client, Commands, Connection, RedisResult};","highlight_start":36,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Commands`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\cache.rs:7:36\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse redis::{AsyncCommands, Client, Commands, Connection, RedisResult};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `severity`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\core.rs","byte_start":15465,"byte_end":15473,"line_start":439,"line_end":439,"column_start":17,"column_end":25,"is_primary":true,"text":[{"text":"                severity,","highlight_start":17,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try removing the field","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\core.rs","byte_start":15465,"byte_end":15474,"line_start":439,"line_end":439,"column_start":17,"column_end":26,"is_primary":true,"text":[{"text":"                severity,","highlight_start":17,"highlight_end":26}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `severity`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\core.rs:439:17\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m439\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                severity,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: try removing the field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 3 previous errors; 28 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 3 previous errors; 28 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0432, E0599.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mSome errors have detailed explanations: E0432, E0599.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0432`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about an error, try `rustc --explain E0432`.\u001b[0m\n"}
