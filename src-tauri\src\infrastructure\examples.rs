//! Infrastructure usage examples and integration demonstrations
//!
//! This module provides examples of how to use the infrastructure components
//! together in real pharmacy management scenarios.

use chrono::Utc;
use serde_json::json;
use uuid::Uuid;

use super::{
    AlertSeverity, AuditEventType, InfrastructureConfig, InfrastructureServices,
    NotificationChannel, NotificationMessage, NotificationPriority, NotificationRecipient,
    NotificationType, PharmacyEvent, SchedulePriority, TransactionType,
};

/// Example: Complete stock update workflow
pub async fn example_stock_update_workflow(
    infrastructure: &InfrastructureServices,
) -> Result<(), Box<dyn std::error::Error>> {
    let product_id = Uuid::now_v7();
    let location_id = Uuid::now_v7();
    let user_id = Uuid::now_v7();

    // 1. Log the stock update in audit trail
    infrastructure
        .audit
        .log_user_action(
            user_id,
            Some("session_123".to_string()),
            "stock_update".to_string(),
            format!(
                "Updated stock for product {} at location {}",
                product_id, location_id
            ),
            Some("product".to_string()),
            Some(product_id),
            Some(json!({
                "old_quantity": 100,
                "new_quantity": 75,
                "reason": "sale"
            })),
        )
        .await?;

    // 2. Update cache with new stock level
    let cache_key = format!("stock:{}:{}", product_id, location_id);
    infrastructure.cache.set(&cache_key, &75i32).await?;

    // 3. Publish stock update event
    infrastructure
        .event_bus
        .publish(PharmacyEvent::StockUpdated {
            product_id,
            location_id,
            old_quantity: rust_decimal::Decimal::from(100),
            new_quantity: rust_decimal::Decimal::from(75),
            reason: "Sale completed".to_string(),
            timestamp: Utc::now(),
        })
        .await?;

    // 4. Check if low stock alert is needed
    let threshold = rust_decimal::Decimal::from(80);
    if rust_decimal::Decimal::from(75) < threshold {
        // Publish low stock alert
        infrastructure
            .event_bus
            .publish(PharmacyEvent::LowStockAlert {
                product_id,
                location_id,
                current_quantity: rust_decimal::Decimal::from(75),
                threshold,
                severity: AlertSeverity::Warning,
                timestamp: Utc::now(),
            })
            .await?;

        // Send notification to pharmacy manager
        let recipient = NotificationRecipient {
            id: Uuid::now_v7(),
            name: "Pharmacy Manager".to_string(),
            email: Some("<EMAIL>".to_string()),
            phone: Some("+1234567890".to_string()),
            push_token: None,
            preferred_channels: vec![NotificationChannel::Email, NotificationChannel::Sms],
            timezone: "UTC".to_string(),
            language: "en".to_string(),
        };

        infrastructure
            .notifications
            .register_recipient(recipient.clone())
            .await;

        let notification = NotificationMessage {
            id: Uuid::now_v7(),
            notification_type: NotificationType::LowStockAlert,
            recipient_id: recipient.id,
            channels: vec![NotificationChannel::Email],
            priority: NotificationPriority::High,
            subject: "Low Stock Alert".to_string(),
            body: format!(
                "Product {} at location {} is running low: {} units remaining (threshold: {})",
                product_id, location_id, 75, threshold
            ),
            template_data: json!({
                "product_id": product_id,
                "location_id": location_id,
                "current_quantity": 75,
                "threshold": threshold
            }),
            scheduled_for: None,
            expires_at: None,
            created_at: Utc::now(),
        };

        infrastructure
            .notifications
            .send_notification(notification)
            .await?;
    }

    println!("Stock update workflow completed successfully");
    Ok(())
}

/// Example: Prescription scheduling workflow
pub async fn example_prescription_workflow(
    infrastructure: &InfrastructureServices,
) -> Result<(), Box<dyn std::error::Error>> {
    let prescription_id = Uuid::now_v7();
    let customer_id = Uuid::now_v7();
    let pharmacist_id = Uuid::now_v7();

    // 1. Cache prescription data
    let prescription_data = json!({
        "id": prescription_id,
        "customer_id": customer_id,
        "medication": "Lisinopril 10mg",
        "quantity": 30,
        "refills": 2,
        "status": "scheduled"
    });

    let cache_key = format!("prescription:{}", prescription_id);
    infrastructure
        .cache
        .set(&cache_key, &prescription_data)
        .await?;

    // 2. Log prescription creation
    infrastructure
        .audit
        .log_user_action(
            pharmacist_id,
            Some("session_456".to_string()),
            "prescription_scheduled".to_string(),
            format!(
                "Scheduled prescription {} for customer {}",
                prescription_id, customer_id
            ),
            Some("prescription".to_string()),
            Some(prescription_id),
            Some(prescription_data.clone()),
        )
        .await?;

    // 3. Publish prescription scheduled event
    infrastructure
        .event_bus
        .publish(PharmacyEvent::PrescriptionScheduled {
            prescription_id,
            customer_id,
            scheduled_date: chrono::Utc::now().date_naive(),
            priority: SchedulePriority::Routine,
            timestamp: Utc::now(),
        })
        .await?;

    // 4. Send notification to customer
    let customer = NotificationRecipient {
        id: customer_id,
        name: "John Doe".to_string(),
        email: Some("<EMAIL>".to_string()),
        phone: Some("+1987654321".to_string()),
        push_token: None,
        preferred_channels: vec![NotificationChannel::Email, NotificationChannel::Sms],
        timezone: "UTC".to_string(),
        language: "en".to_string(),
    };

    infrastructure
        .notifications
        .register_recipient(customer.clone())
        .await;

    let notification = NotificationMessage {
        id: Uuid::now_v7(),
        notification_type: NotificationType::PrescriptionReady,
        recipient_id: customer.id,
        channels: vec![NotificationChannel::Email],
        priority: NotificationPriority::Normal,
        subject: "Prescription Scheduled".to_string(),
        body: format!(
            "Your prescription {} has been scheduled and will be ready for pickup.",
            prescription_id
        ),
        template_data: json!({
            "prescription_id": prescription_id,
            "customer_name": customer.name,
            "medication": "Lisinopril 10mg"
        }),
        scheduled_for: None,
        expires_at: None,
        created_at: Utc::now(),
    };

    infrastructure
        .notifications
        .send_notification(notification)
        .await?;

    println!("Prescription workflow completed successfully");
    Ok(())
}

/// Example: System health monitoring
pub async fn example_health_monitoring(
    infrastructure: &InfrastructureServices,
) -> Result<(), Box<dyn std::error::Error>> {
    // Perform health check
    let health = infrastructure.check_health().await;

    println!("Infrastructure Health Status:");
    println!("Overall Healthy: {}", health.overall_healthy);
    println!("Last Check: {}", health.last_check);

    for (service, status) in &health.services {
        println!(
            "  {}: {} ({}ms)",
            service,
            if status.is_healthy { "✓" } else { "✗" },
            status.response_time_ms
        );

        if let Some(error) = &status.error_message {
            println!("    Error: {}", error);
        }
    }

    // Log health check in audit trail
    infrastructure
        .audit
        .log_system_event(
            AuditEventType::SystemStartup,
            format!(
                "Health check completed: {} services healthy",
                health.services.iter().filter(|(_, h)| h.is_healthy).count()
            ),
            Some(json!({
                "overall_healthy": health.overall_healthy,
                "services_checked": health.services.len(),
                "healthy_services": health.services.iter().filter(|(_, h)| h.is_healthy).count()
            })),
        )
        .await?;

    Ok(())
}

/// Example: Financial transaction processing
pub async fn example_financial_transaction(
    infrastructure: &InfrastructureServices,
) -> Result<(), Box<dyn std::error::Error>> {
    let transaction_id = Uuid::now_v7();
    let account_id = Uuid::now_v7();
    let amount = rust_decimal::Decimal::from(125);

    // 1. Cache transaction for quick access
    let transaction_data = json!({
        "id": transaction_id,
        "type": "sale",
        "amount": amount,
        "account_id": account_id,
        "timestamp": Utc::now()
    });

    let cache_key = format!("transaction:{}", transaction_id);
    infrastructure
        .cache
        .set_with_ttl(
            &cache_key,
            &transaction_data,
            std::time::Duration::from_secs(3600),
        )
        .await?;

    // 2. Log transaction in audit trail
    infrastructure
        .audit
        .log_system_event(
            AuditEventType::SaleCompleted,
            format!(
                "Financial transaction {} processed: ${}",
                transaction_id, amount
            ),
            Some(transaction_data),
        )
        .await?;

    // 3. Publish financial transaction event
    infrastructure
        .event_bus
        .publish(PharmacyEvent::FinancialTransaction {
            transaction_id,
            transaction_type: TransactionType::Sale,
            amount,
            account_id,
            timestamp: Utc::now(),
        })
        .await?;

    println!("Financial transaction processed successfully");
    Ok(())
}

/// Example: Complete infrastructure initialization and usage
pub async fn example_complete_workflow() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize infrastructure with default configuration
    let config = InfrastructureConfig::default();
    let infrastructure = InfrastructureServices::initialize(config).await?;

    println!("Infrastructure initialized successfully");

    // Run example workflows
    example_health_monitoring(&infrastructure).await?;
    example_stock_update_workflow(&infrastructure).await?;
    example_prescription_workflow(&infrastructure).await?;
    example_financial_transaction(&infrastructure).await?;

    // Get final statistics
    let event_stats = infrastructure.event_bus.get_stats().await;
    let cache_stats = infrastructure.cache.get_stats().await?;
    let audit_stats = infrastructure.audit.get_stats().await;

    println!("\nFinal Statistics:");
    println!("Events Published: {}", event_stats.events_published);
    println!("Events Processed: {}", event_stats.events_processed);
    println!("Cache Hit Rate: {:.2}%", cache_stats.hit_rate() * 100.0);
    println!("Audit Events Logged: {}", audit_stats.events_logged);

    // Graceful shutdown
    infrastructure.shutdown().await?;
    println!("Infrastructure shutdown completed");

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_example_workflows() {
        // This test would require actual Redis and database connections
        // In a real environment, you would set up test containers or mock services
        println!("Example workflows would be tested with proper infrastructure setup");
    }
}
