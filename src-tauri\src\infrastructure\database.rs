//! Database connection pooling and transaction management for concurrent operations
//!
//! This module provides database connection pooling, transaction management, and
//! high-performance database operations for the pharmacy management system.

use chrono::{DateTime, Utc};
use sea_orm::{
    ConnectOptions, Database, DatabaseConnection, DatabaseTransaction, DbErr, TransactionTrait,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::time::Duration;
use thiserror::Error;
use tokio::sync::RwLock;

/// Errors that can occur in database operations
#[derive(Error, Debug)]
pub enum DatabaseError {
    #[error("Database connection error: {0}")]
    ConnectionError(#[from] DbErr),
    #[error("Transaction error: {0}")]
    TransactionError(String),
    #[error("Connection pool exhausted")]
    PoolExhausted,
    #[error("Database configuration error: {0}")]
    ConfigurationError(String),
    #[error("Query timeout")]
    QueryTimeout,
    #[error("Deadlock detected")]
    DeadlockDetected,
    #[error("Constraint violation: {0}")]
    ConstraintViolation(String),
}

/// Database configuration
#[derive(Debug, Clone)]
pub struct DatabaseConfig {
    pub database_url: String,
    pub max_connections: u32,
    pub min_connections: u32,
    pub connect_timeout: Duration,
    pub idle_timeout: Duration,
    pub max_lifetime: Duration,
    pub acquire_timeout: Duration,
    pub enable_logging: bool,
    pub log_level: String,
    pub enable_metrics: bool,
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            database_url: "sqlite://pharmacy.db".to_string(),
            max_connections: 20,
            min_connections: 5,
            connect_timeout: Duration::from_secs(30),
            idle_timeout: Duration::from_secs(600), // 10 minutes
            max_lifetime: Duration::from_secs(3600), // 1 hour
            acquire_timeout: Duration::from_secs(10),
            enable_logging: true,
            log_level: "info".to_string(),
            enable_metrics: true,
        }
    }
}

/// Connection pool statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PoolStats {
    pub total_connections: u32,
    pub active_connections: u32,
    pub idle_connections: u32,
    pub pending_requests: u32,
    pub total_queries: u64,
    pub successful_queries: u64,
    pub failed_queries: u64,
    pub average_query_time_ms: f64,
    pub connection_errors: u64,
    pub deadlocks: u64,
    pub timeouts: u64,
}

impl Default for PoolStats {
    fn default() -> Self {
        Self {
            total_connections: 0,
            active_connections: 0,
            idle_connections: 0,
            pending_requests: 0,
            total_queries: 0,
            successful_queries: 0,
            failed_queries: 0,
            average_query_time_ms: 0.0,
            connection_errors: 0,
            deadlocks: 0,
            timeouts: 0,
        }
    }
}

/// Database connection pool manager
pub struct ConnectionPool {
    connection: Arc<DatabaseConnection>,
    config: DatabaseConfig,
    stats: Arc<RwLock<PoolStats>>,
}

impl ConnectionPool {
    /// Create a new connection pool
    pub async fn new(config: DatabaseConfig) -> Result<Self, DatabaseError> {
        let mut opt = ConnectOptions::new(&config.database_url);
        opt.max_connections(config.max_connections)
            .min_connections(config.min_connections)
            .connect_timeout(config.connect_timeout)
            .idle_timeout(config.idle_timeout)
            .max_lifetime(config.max_lifetime)
            .acquire_timeout(config.acquire_timeout)
            .sqlx_logging(config.enable_logging);

        let connection = Arc::new(Database::connect(opt).await?);

        // Test the connection
        connection.ping().await?;

        let pool = Self {
            connection,
            config,
            stats: Arc::new(RwLock::new(PoolStats::default())),
        };

        tracing::info!(
            "Database connection pool initialized with {} max connections",
            pool.config.max_connections
        );

        Ok(pool)
    }

    /// Get a database connection
    pub fn get_connection(&self) -> &DatabaseConnection {
        &self.connection
    }

    /// Execute a query with metrics tracking
    pub async fn execute_with_metrics<F, R>(&self, operation: F) -> Result<R, DatabaseError>
    where
        F: FnOnce(
            &DatabaseConnection,
        ) -> std::pin::Pin<
            Box<dyn std::future::Future<Output = Result<R, DbErr>> + Send + '_>,
        >,
    {
        let start_time = std::time::Instant::now();

        // Update stats - query started
        {
            let mut stats = self.stats.write().await;
            stats.total_queries += 1;
        }

        let result = operation(&self.connection).await;

        let duration = start_time.elapsed();

        // Update stats - query completed
        {
            let mut stats = self.stats.write().await;
            match &result {
                Ok(_) => stats.successful_queries += 1,
                Err(_) => stats.failed_queries += 1,
            }

            // Update average query time (simple moving average)
            let total_successful = stats.successful_queries + stats.failed_queries;
            if total_successful > 0 {
                stats.average_query_time_ms = (stats.average_query_time_ms
                    * (total_successful - 1) as f64
                    + duration.as_millis() as f64)
                    / total_successful as f64;
            }
        }

        result.map_err(DatabaseError::from)
    }

    /// Get pool statistics
    pub async fn get_stats(&self) -> PoolStats {
        self.stats.read().await.clone()
    }

    /// Health check for the database connection
    pub async fn health_check(&self) -> Result<DatabaseHealthStatus, DatabaseError> {
        let start_time = std::time::Instant::now();

        match self.connection.ping().await {
            Ok(_) => {
                let response_time = start_time.elapsed();
                Ok(DatabaseHealthStatus {
                    is_healthy: true,
                    response_time_ms: response_time.as_millis() as u64,
                    error_message: None,
                    last_check: Utc::now(),
                })
            }
            Err(e) => {
                let response_time = start_time.elapsed();
                Ok(DatabaseHealthStatus {
                    is_healthy: false,
                    response_time_ms: response_time.as_millis() as u64,
                    error_message: Some(e.to_string()),
                    last_check: Utc::now(),
                })
            }
        }
    }
}

impl Clone for ConnectionPool {
    fn clone(&self) -> Self {
        Self {
            connection: self.connection.clone(),
            config: self.config.clone(),
            stats: self.stats.clone(),
        }
    }
}

/// Database health status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseHealthStatus {
    pub is_healthy: bool,
    pub response_time_ms: u64,
    pub error_message: Option<String>,
    pub last_check: DateTime<Utc>,
}

/// Transaction manager for handling complex database operations
pub struct TransactionManager {
    pool: ConnectionPool,
}

impl TransactionManager {
    /// Create a new transaction manager
    pub fn new(pool: ConnectionPool) -> Self {
        Self { pool }
    }

    /// Execute operations within a transaction
    pub async fn execute_transaction<F, R>(&self, operation: F) -> Result<R, DatabaseError>
    where
        F: for<'a> FnOnce(
            &'a DatabaseTransaction,
        ) -> std::pin::Pin<
            Box<dyn std::future::Future<Output = Result<R, DatabaseError>> + Send + 'a>,
        >,
    {
        let txn = self.pool.connection.begin().await?;

        match operation(&txn).await {
            Ok(result) => {
                txn.commit().await?;
                tracing::debug!("Transaction committed successfully");
                Ok(result)
            }
            Err(e) => {
                if let Err(rollback_err) = txn.rollback().await {
                    tracing::error!("Failed to rollback transaction: {}", rollback_err);
                } else {
                    tracing::debug!("Transaction rolled back due to error: {}", e);
                }
                Err(e)
            }
        }
    }

    /// Execute operations with retry logic for deadlocks
    pub async fn execute_with_retry<F, R>(
        &self,
        operation: F,
        max_retries: u32,
    ) -> Result<R, DatabaseError>
    where
        F: Fn() -> std::pin::Pin<
            Box<dyn std::future::Future<Output = Result<R, DatabaseError>> + Send + 'static>,
        >,
    {
        let mut attempts = 0;

        loop {
            match operation().await {
                Ok(result) => return Ok(result),
                Err(DatabaseError::DeadlockDetected) if attempts < max_retries => {
                    attempts += 1;
                    let delay = Duration::from_millis(100 * attempts as u64); // Exponential backoff
                    tracing::warn!(
                        "Deadlock detected, retrying in {:?} (attempt {}/{})",
                        delay,
                        attempts,
                        max_retries
                    );
                    tokio::time::sleep(delay).await;

                    // Update deadlock stats
                    {
                        let mut stats = self.pool.stats.write().await;
                        stats.deadlocks += 1;
                    }
                }
                Err(e) => return Err(e),
            }
        }
    }

    /// Execute a batch of operations in a single transaction
    pub async fn execute_batch<F>(&self, operations: Vec<F>) -> Result<Vec<()>, DatabaseError>
    where
        F: for<'a> FnOnce(
                &'a DatabaseTransaction,
            ) -> std::pin::Pin<
                Box<dyn std::future::Future<Output = Result<(), DatabaseError>> + Send + 'a>,
            > + Send
            + 'static,
    {
        self.execute_transaction(|txn| {
            Box::pin(async move {
                let mut results = Vec::new();

                for operation in operations {
                    operation(txn).await?;
                    results.push(());
                }

                Ok(results)
            })
        })
        .await
    }
}

/// Database manager that coordinates all database operations
pub struct DatabaseManager {
    pool: ConnectionPool,
    transaction_manager: TransactionManager,
    config: DatabaseConfig,
}

impl DatabaseManager {
    /// Create a new database manager
    pub async fn new(config: DatabaseConfig) -> Result<Self, DatabaseError> {
        let pool = ConnectionPool::new(config.clone()).await?;
        let transaction_manager = TransactionManager::new(pool.clone());

        Ok(Self {
            pool,
            transaction_manager,
            config,
        })
    }

    /// Get the connection pool
    pub fn pool(&self) -> &ConnectionPool {
        &self.pool
    }

    /// Get the transaction manager
    pub fn transaction_manager(&self) -> &TransactionManager {
        &self.transaction_manager
    }

    /// Get a database connection
    pub fn connection(&self) -> &DatabaseConnection {
        self.pool.get_connection()
    }

    /// Execute a simple query
    pub async fn execute_query<F, R>(&self, query: F) -> Result<R, DatabaseError>
    where
        F: FnOnce(
            &DatabaseConnection,
        ) -> std::pin::Pin<
            Box<dyn std::future::Future<Output = Result<R, DbErr>> + Send + '_>,
        >,
    {
        self.pool.execute_with_metrics(query).await
    }

    /// Execute operations within a transaction
    pub async fn execute_transaction<F, R>(&self, operation: F) -> Result<R, DatabaseError>
    where
        F: for<'a> FnOnce(
            &'a DatabaseTransaction,
        ) -> std::pin::Pin<
            Box<dyn std::future::Future<Output = Result<R, DatabaseError>> + Send + 'a>,
        >,
    {
        self.transaction_manager
            .execute_transaction(operation)
            .await
    }

    /// Execute operations with retry logic
    pub async fn execute_with_retry<F, R>(
        &self,
        operation: F,
        max_retries: u32,
    ) -> Result<R, DatabaseError>
    where
        F: Fn() -> std::pin::Pin<
            Box<dyn std::future::Future<Output = Result<R, DatabaseError>> + Send + 'static>,
        >,
    {
        self.transaction_manager
            .execute_with_retry(operation, max_retries)
            .await
    }

    /// Get comprehensive database statistics
    pub async fn get_comprehensive_stats(&self) -> DatabaseStats {
        let pool_stats = self.pool.get_stats().await;
        let health_status =
            self.pool
                .health_check()
                .await
                .unwrap_or_else(|_| DatabaseHealthStatus {
                    is_healthy: false,
                    response_time_ms: 0,
                    error_message: Some("Health check failed".to_string()),
                    last_check: Utc::now(),
                });

        DatabaseStats {
            pool_stats,
            health_status,
            config: DatabaseStatsConfig {
                max_connections: self.config.max_connections,
                min_connections: self.config.min_connections,
                connect_timeout_ms: self.config.connect_timeout.as_millis() as u64,
                idle_timeout_ms: self.config.idle_timeout.as_millis() as u64,
            },
        }
    }

    /// Perform database maintenance operations
    pub async fn perform_maintenance(&self) -> Result<MaintenanceResult, DatabaseError> {
        tracing::info!("Starting database maintenance");

        let start_time = std::time::Instant::now();
        let mut operations_performed = Vec::new();

        // In a real implementation, this would perform various maintenance tasks:
        // - VACUUM operations for SQLite
        // - ANALYZE operations for query optimization
        // - Index maintenance
        // - Statistics updates
        // - Connection pool cleanup

        operations_performed.push("Connection pool health check".to_string());
        operations_performed.push("Query statistics update".to_string());

        let duration = start_time.elapsed();

        let result = MaintenanceResult {
            started_at: Utc::now() - chrono::Duration::milliseconds(duration.as_millis() as i64),
            completed_at: Utc::now(),
            duration_ms: duration.as_millis() as u64,
            operations_performed,
            errors: Vec::new(),
        };

        tracing::info!("Database maintenance completed in {:?}", duration);
        Ok(result)
    }

    /// Close all database connections
    pub async fn close(&self) -> Result<(), DatabaseError> {
        // In SeaORM, connections are automatically closed when dropped
        tracing::info!("Database manager shutting down");
        Ok(())
    }
}

impl Clone for DatabaseManager {
    fn clone(&self) -> Self {
        Self {
            pool: self.pool.clone(),
            transaction_manager: TransactionManager::new(self.pool.clone()),
            config: self.config.clone(),
        }
    }
}

/// Comprehensive database statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseStats {
    pub pool_stats: PoolStats,
    pub health_status: DatabaseHealthStatus,
    pub config: DatabaseStatsConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseStatsConfig {
    pub max_connections: u32,
    pub min_connections: u32,
    pub connect_timeout_ms: u64,
    pub idle_timeout_ms: u64,
}

/// Database maintenance result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MaintenanceResult {
    pub started_at: DateTime<Utc>,
    pub completed_at: DateTime<Utc>,
    pub duration_ms: u64,
    pub operations_performed: Vec<String>,
    pub errors: Vec<String>,
}

/// Database migration manager
pub struct MigrationManager {
    connection: DatabaseConnection,
}

impl MigrationManager {
    /// Create a new migration manager
    pub fn new(connection: DatabaseConnection) -> Self {
        Self { connection }
    }

    /// Run pending migrations
    pub async fn run_migrations(&self) -> Result<MigrationResult, DatabaseError> {
        tracing::info!("Running database migrations");

        // In a real implementation, this would use SeaORM migrations
        // For now, we'll simulate the migration process

        let result = MigrationResult {
            migrations_run: vec![
                "001_initial_schema".to_string(),
                "002_add_audit_tables".to_string(),
                "003_add_indexes".to_string(),
            ],
            started_at: Utc::now(),
            completed_at: Utc::now(),
            success: true,
            error_message: None,
        };

        tracing::info!("Database migrations completed successfully");
        Ok(result)
    }

    /// Check migration status
    pub async fn check_migration_status(&self) -> Result<Vec<MigrationStatus>, DatabaseError> {
        // In a real implementation, this would check the migration table
        Ok(vec![
            MigrationStatus {
                name: "001_initial_schema".to_string(),
                applied: true,
                applied_at: Some(Utc::now()),
            },
            MigrationStatus {
                name: "002_add_audit_tables".to_string(),
                applied: true,
                applied_at: Some(Utc::now()),
            },
        ])
    }
}

/// Migration result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MigrationResult {
    pub migrations_run: Vec<String>,
    pub started_at: DateTime<Utc>,
    pub completed_at: DateTime<Utc>,
    pub success: bool,
    pub error_message: Option<String>,
}

/// Migration status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MigrationStatus {
    pub name: String,
    pub applied: bool,
    pub applied_at: Option<DateTime<Utc>>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_database_config_default() {
        let config = DatabaseConfig::default();
        assert_eq!(config.max_connections, 20);
        assert_eq!(config.min_connections, 5);
        assert!(config.enable_logging);
    }

    #[tokio::test]
    async fn test_pool_stats_default() {
        let stats = PoolStats::default();
        assert_eq!(stats.total_connections, 0);
        assert_eq!(stats.total_queries, 0);
        assert_eq!(stats.average_query_time_ms, 0.0);
    }

    #[test]
    fn test_database_health_status() {
        let health = DatabaseHealthStatus {
            is_healthy: true,
            response_time_ms: 50,
            error_message: None,
            last_check: Utc::now(),
        };

        assert!(health.is_healthy);
        assert_eq!(health.response_time_ms, 50);
        assert!(health.error_message.is_none());
    }
}
