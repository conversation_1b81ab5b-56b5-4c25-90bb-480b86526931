{"rustc": 16591470773350601817, "features": "[\"backend-mysql\", \"backend-postgres\", \"backend-sqlite\", \"bigdecimal\", \"chrono\", \"derive\", \"hashable-value\", \"ordered-float\", \"postgres-array\", \"rust_decimal\", \"sea-query-derive\", \"serde_json\", \"thread-safe\", \"time\", \"uuid\", \"with-bigdecimal\", \"with-chrono\", \"with-json\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "declared_features": "[\"all-features\", \"all-types\", \"attr\", \"backend-mysql\", \"backend-postgres\", \"backend-sqlite\", \"bigdecimal\", \"chrono\", \"default\", \"derive\", \"hashable-value\", \"ipnetwork\", \"mac_address\", \"option-more-parentheses\", \"option-sqlite-exact-column-type\", \"ordered-float\", \"pgvector\", \"postgres-array\", \"postgres-interval\", \"postgres-types\", \"postgres-vector\", \"rust_decimal\", \"sea-query-derive\", \"serde_json\", \"tests-cfg\", \"thread-safe\", \"time\", \"uuid\", \"with-bigdecimal\", \"with-chrono\", \"with-ipnetwork\", \"with-json\", \"with-mac_address\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "target": 17362542534165460642, "profile": 2241668132362809309, "path": 1105030420448245761, "deps": [[1117455883785908841, "inherent", false, 2399215757878492937], [8319709847752024821, "uuid", false, 4375624582824923410], [9897246384292347999, "chrono", false, 11565035621207823088], [11074247395802926746, "ordered_float", false, 9805046848869806831], [12409575957772518135, "time", false, 6202611441483771785], [14647456484942590313, "bigdecimal", false, 16021671231134990048], [14806645394729624434, "sea_query_derive", false, 9843396804295671385], [15367738274754116744, "serde_json", false, 13370400617386028636], [16682465660942253309, "rust_decimal", false, 7608462804829237094]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sea-query-963770e9dea9c6e9\\dep-lib-sea_query", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}