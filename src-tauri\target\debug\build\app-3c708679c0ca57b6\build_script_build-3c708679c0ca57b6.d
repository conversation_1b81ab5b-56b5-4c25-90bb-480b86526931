D:\programming\desktop-apps\latest-meditrack\meditrack\src-tauri\target\debug\build\app-3c708679c0ca57b6\build_script_build-3c708679c0ca57b6.exe: build.rs Cargo.toml

D:\programming\desktop-apps\latest-meditrack\meditrack\src-tauri\target\debug\build\app-3c708679c0ca57b6\build_script_build-3c708679c0ca57b6.d: build.rs Cargo.toml

build.rs:
Cargo.toml:

# env-dep:CLIPPY_ARGS=
# env-dep:CLIPPY_CONF_DIR
