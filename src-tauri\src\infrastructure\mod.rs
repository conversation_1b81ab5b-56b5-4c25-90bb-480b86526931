//! Core infrastructure modules for the pharmacy management system
//!
//! This module provides the foundational infrastructure components including:
//! - Event bus system for real-time communication
//! - Redis cache layer for high-performance data access
//! - Notification service with multiple delivery channels
//! - Audit trail manager with tamper-proof logging
//! - Database connection pooling and transaction management

pub mod audit;
pub mod cache;
pub mod core;
pub mod database;
pub mod event_bus;
pub mod examples;
pub mod notifications;

pub use audit::{
    AuditConfig, AuditError, AuditEvent, AuditEventType, AuditSeverity, AuditTrailManager,
};
pub use cache::{CacheConfig, CacheError, CacheService};
pub use core::{InfrastructureConfig, InfrastructureServices};
pub use database::{DatabaseConfig, DatabaseError, DatabaseManager};
pub use event_bus::{
    AlertSeverity, EventBus, EventBusError, EventHandler, PharmacyEvent, SchedulePriority,
    TransactionType,
};
pub use notifications::{
    NotificationChannel, NotificationConfig, NotificationError, NotificationMessage,
    NotificationPriority, NotificationRecipient, NotificationService, NotificationType,
};
