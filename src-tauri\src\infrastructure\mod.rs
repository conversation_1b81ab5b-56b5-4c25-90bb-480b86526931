//! Core infrastructure modules for the pharmacy management system
//!
//! This module provides the foundational infrastructure components including:
//! - Event bus system for real-time communication
//! - Redis cache layer for high-performance data access
//! - Notification service with multiple delivery channels
//! - Audit trail manager with tamper-proof logging
//! - Database connection pooling and transaction management

pub mod audit;
pub mod cache;
pub mod core;
pub mod database;
pub mod event_bus;
pub mod examples;
pub mod notifications;

pub use audit::{
    AuditConfig, AuditError, AuditEvent, AuditEventType, AuditSeverity, AuditTrailManager,
    ComplianceReport, ComplianceReportType,
};
pub use cache::{CacheConfig, CacheError, CacheService, SessionManager};
pub use core::{
    InfrastructureConfig, InfrastructureError, InfrastructureHealth, InfrastructureServices,
    ServiceHealth,
};
pub use database::{
    ConnectionPool, DatabaseConfig, DatabaseError, DatabaseManager, DatabaseStats,
    MigrationManager, TransactionManager,
};
pub use event_bus::{EventBus, EventBusError, EventHandler, PharmacyEvent};
pub use notifications::{
    EmailProvider, NotificationChannel, NotificationConfig, NotificationError, NotificationMessage,
    NotificationProvider, NotificationRecipient, NotificationService, PushProvider, SmsProvider,
};
