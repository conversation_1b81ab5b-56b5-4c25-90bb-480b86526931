//! Core infrastructure modules for the pharmacy management system
//!
//! This module provides the foundational infrastructure components including:
//! - Event bus system for real-time communication
//! - Redis cache layer for high-performance data access
//! - Notification service with multiple delivery channels
//! - Audit trail manager with tamper-proof logging
//! - Database connection pooling and transaction management

pub mod event_bus;
pub mod cache;
pub mod notifications;
pub mod audit;
pub mod database;
pub mod core;
pub mod examples;

pub use event_bus::{EventBus, PharmacyEvent, EventHandler, EventBusError};
pub use cache::{CacheService, CacheError, CacheConfig, SessionManager};
pub use notifications::{
    NotificationService, NotificationChannel, NotificationError, NotificationMessage,
    NotificationRecipient, NotificationProvider, EmailProvider, SmsProvider, PushProvider
};
pub use audit::{
    AuditTrailManager, AuditEvent, AuditError, AuditEventType, AuditSeverity,
    AuditConfig, ComplianceReport, ComplianceReportType
};
pub use database::{
    DatabaseManager, ConnectionPool, TransactionManager, DatabaseError, DatabaseConfig,
    DatabaseStats, MigrationManager
};
pub use core::{
    InfrastructureServices, InfrastructureConfig, InfrastructureError,
    InfrastructureHealth, ServiceHealth
};