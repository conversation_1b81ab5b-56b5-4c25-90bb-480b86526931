{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 2241668132362809309, "path": 13173659658734678530, "deps": [[376837177317575824, "softbuffer", false, 11666857911912498663], [442785307232013896, "tauri_runtime", false, 3617901434420635384], [3150220818285335163, "url", false, 3569982634382302943], [3722963349756955755, "once_cell", false, 2363945442117183028], [4143744114649553716, "raw_window_handle", false, 8084507311091100797], [5986029879202738730, "log", false, 13404088029148696127], [7752760652095876438, "build_script_build", false, 14646362836042100109], [8539587424388551196, "webview2_com", false, 16629564564922351208], [9010263965687315507, "http", false, 15283923244554771932], [11050281405049894993, "tauri_utils", false, 5803446630889171054], [13116089016666501665, "windows", false, 6388814616047702111], [13223659721939363523, "tao", false, 6300224415277704236], [14794439852947137341, "wry", false, 4497648382508884786]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-c04d235ec2593293\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}